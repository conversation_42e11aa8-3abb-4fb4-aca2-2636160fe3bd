#!/usr/bin/env python3
"""
Simple script to run the backend server.
This handles activating the virtual environment and starting uvicorn.
"""

import subprocess
import sys
import os
from pathlib import Path

def main():
    project_root = Path(__file__).parent
    backend_dir = project_root / "backend"
    venv_path = backend_dir / "venv"
    
    # Check if virtual environment exists
    if not venv_path.exists():
        print("Virtual environment not found. Please run setup.py first.")
        return False
    
    # Determine the correct python executable in venv
    if os.name == 'nt':  # Windows
        python_exe = venv_path / "Scripts" / "python.exe"
    else:  # Unix/Linux/macOS
        python_exe = venv_path / "bin" / "python"
    
    # Check for .env file
    env_file = project_root / ".env"
    if not env_file.exists():
        print("Warning: .env file not found. Make sure to set OPENAI_API_KEY environment variable.")
    
    # Load environment variables from .env if it exists
    env = os.environ.copy()
    if env_file.exists():
        with open(env_file) as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    env[key] = value
    
    print("Starting backend server...")
    print("Server will be available at: http://localhost:8000")
    print("API docs will be available at: http://localhost:8000/docs")
    print("Press Ctrl+C to stop the server")
    
    # Run uvicorn
    cmd = [str(python_exe), "-m", "uvicorn", "main:app", "--reload", "--port", "8000", "--host", "0.0.0.0"]
    try:
        subprocess.run(cmd, cwd=backend_dir, env=env)
    except KeyboardInterrupt:
        print("\nServer stopped.")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
