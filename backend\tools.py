# backend/tools.py
import subprocess
import tempfile
import os
from pathlib import Path
from typing import Tuple

# Safe python runner: writes code to a temp file and runs in subprocess with timeout
def run_python(code: str, timeout: int = 5) -> str:
    with tempfile.TemporaryDirectory() as td:
        path = Path(td) / "script.py"
        path.write_text(code)
        try:
            # Use -u for unbuffered output
            # Use 'python' on Windows, 'python3' on Unix-like systems
            python_cmd = "python" if os.name == 'nt' else "python3"
            proc = subprocess.run(
                [python_cmd, str(path)],
                capture_output=True, text=True, timeout=timeout, check=False
            )
            out = proc.stdout.strip()
            err = proc.stderr.strip()
            if proc.returncode != 0:
                return f"ERROR (exit {proc.returncode}):\n{err}\n{out}"
            return out or "(no output)"
        except subprocess.TimeoutExpired:
            return "ERROR: execution timed out"
        except Exception as e:
            return f"ERROR: {e}"

# File tools (constrained to project folder for safety)
ROOT = Path(__file__).resolve().parents[1]
ALLOWED_BASE = ROOT

def read_file(path: str) -> str:
    target = (ALLOWED_BASE / path).resolve()
    if not str(target).startswith(str(ALLOWED_BASE)):
        return "ERROR: access denied"
    if not target.exists():
        return "ERROR: file not found"
    return target.read_text()

def write_file(path: str, content: str) -> str:
    target = (ALLOWED_BASE / path).resolve()
    if not str(target).startswith(str(ALLOWED_BASE)):
        return "ERROR: access denied"
    target.parent.mkdir(parents=True, exist_ok=True)
    target.write_text(content)
    return "OK"
