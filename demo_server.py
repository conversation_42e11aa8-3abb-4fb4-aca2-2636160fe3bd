#!/usr/bin/env python3
"""
Simple demo server that works without OpenAI API.
This demonstrates the basic functionality of the augmented agent.
"""

import sys
import os
import subprocess
import tempfile
from pathlib import Path
from http.server import HTTPServer, BaseHTTPRequestHandler
import json
import urllib.parse

# Add backend to path
backend_path = Path(__file__).parent / "backend"
sys.path.insert(0, str(backend_path))

# Simple tools implementation
def run_python_demo(code: str, timeout: int = 5) -> str:
    """Run Python code safely in a temporary file."""
    with tempfile.TemporaryDirectory() as td:
        path = Path(td) / "script.py"
        path.write_text(code)
        try:
            python_cmd = "python" if os.name == 'nt' else "python3"
            proc = subprocess.run(
                [python_cmd, str(path)],
                capture_output=True, text=True, timeout=timeout, check=False
            )
            out = proc.stdout.strip()
            err = proc.stderr.strip()
            if proc.returncode != 0:
                return f"ERROR (exit {proc.returncode}):\n{err}\n{out}"
            return out or "(no output)"
        except subprocess.TimeoutExpired:
            return "ERROR: execution timed out"
        except Exception as e:
            return f"ERROR: {e}"

def demo_agent_response(user_input: str) -> str:
    """Generate a demo response without using OpenAI API."""
    user_input_lower = user_input.lower()
    
    # Simple pattern matching for demo purposes
    if "factorial" in user_input_lower:
        code = """
def factorial(n):
    if n <= 1:
        return 1
    return n * factorial(n - 1)

result = factorial(5)
print(f"Factorial of 5 is: {result}")
"""
        result = run_python_demo(code)
        return f"I'll calculate the factorial for you using Python:\n\n{result}"
    
    elif "fibonacci" in user_input_lower:
        code = """
def fibonacci(n):
    fib_sequence = [0, 1]
    for i in range(2, n):
        fib_sequence.append(fib_sequence[i-1] + fib_sequence[i-2])
    return fib_sequence

fib_numbers = fibonacci(10)
print("First 10 Fibonacci numbers:")
for i, num in enumerate(fib_numbers):
    print(f"F({i}) = {num}")
"""
        result = run_python_demo(code)
        return f"Here are the first 10 Fibonacci numbers:\n\n{result}"
    
    elif "reverse" in user_input_lower and "string" in user_input_lower:
        code = """
def reverse_string(s):
    return s[::-1]

# Test the function
test_string = "Hello World"
reversed_str = reverse_string(test_string)
print(f"Original: {test_string}")
print(f"Reversed: {reversed_str}")

# Another way using a loop
def reverse_string_loop(s):
    result = ""
    for char in s:
        result = char + result
    return result

reversed_str2 = reverse_string_loop(test_string)
print(f"Reversed (using loop): {reversed_str2}")
"""
        result = run_python_demo(code)
        return f"Here's a Python function to reverse a string:\n\n{result}"
    
    elif "calculate" in user_input_lower or "math" in user_input_lower:
        code = """
import math

# Some calculations
print("Basic calculations:")
print(f"2 + 2 = {2 + 2}")
print(f"10 * 5 = {10 * 5}")
print(f"Square root of 16 = {math.sqrt(16)}")
print(f"2 to the power of 8 = {2**8}")
print(f"Pi = {math.pi:.6f}")
"""
        result = run_python_demo(code)
        return f"Here are some mathematical calculations:\n\n{result}"
    
    elif "hello" in user_input_lower or "hi" in user_input_lower:
        code = """
print("Hello from Python!")
print("I'm running inside the augmented agent!")
print("Current time:", end=" ")
import datetime
print(datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
"""
        result = run_python_demo(code)
        return f"Hello! Let me greet you with some Python code:\n\n{result}"
    
    else:
        # Default response with a simple Python example
        code = """
print("Welcome to the Augmented Agent Demo!")
print("I can run Python code for you.")
print("Try asking me to:")
print("- Calculate factorial")
print("- Generate Fibonacci numbers")
print("- Reverse a string")
print("- Do mathematical calculations")

# Here's a simple example:
numbers = [1, 2, 3, 4, 5]
squared = [x**2 for x in numbers]
print(f"Numbers: {numbers}")
print(f"Squared: {squared}")
"""
        result = run_python_demo(code)
        return f"I'm a demo version of the augmented agent. Here's what I can do:\n\n{result}"

class DemoHandler(BaseHTTPRequestHandler):
    def do_OPTIONS(self):
        """Handle CORS preflight requests."""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()

    def do_GET(self):
        """Handle GET requests."""
        if self.path == '/':
            self.send_response(200)
            self.send_header('Content-Type', 'text/html')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            self.wfile.write(b'<h1>Augmented Agent Demo Server</h1><p>Server is running! Use the frontend to chat.</p>')
        elif self.path == '/docs':
            self.send_response(200)
            self.send_header('Content-Type', 'text/html')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            self.wfile.write(b'<h1>Demo API</h1><p>POST to /chat with JSON: {"messages": [], "input": "your message"}</p>')
        else:
            self.send_response(404)
            self.end_headers()

    def do_POST(self):
        """Handle POST requests."""
        if self.path == '/chat':
            try:
                content_length = int(self.headers['Content-Length'])
                post_data = self.rfile.read(content_length)
                data = json.loads(post_data.decode('utf-8'))
                
                user_input = data.get('input', '')
                reply = demo_agent_response(user_input)
                
                response = {'reply': reply}
                
                self.send_response(200)
                self.send_header('Content-Type', 'application/json')
                self.send_header('Access-Control-Allow-Origin', '*')
                self.end_headers()
                self.wfile.write(json.dumps(response).encode('utf-8'))
                
            except Exception as e:
                self.send_response(500)
                self.send_header('Content-Type', 'application/json')
                self.send_header('Access-Control-Allow-Origin', '*')
                self.end_headers()
                error_response = {'reply': f'Error: {str(e)}'}
                self.wfile.write(json.dumps(error_response).encode('utf-8'))
        else:
            self.send_response(404)
            self.end_headers()

def main():
    port = 8000
    server = HTTPServer(('0.0.0.0', port), DemoHandler)
    print(f"🚀 Demo server starting on http://localhost:{port}")
    print("📱 Open the frontend at: file:///d:/aug/frontend/index.html")
    print("📚 API docs at: http://localhost:{port}/docs")
    print("🛑 Press Ctrl+C to stop the server")
    
    try:
        server.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 Server stopped.")
        server.shutdown()

if __name__ == '__main__':
    main()
