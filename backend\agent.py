# backend/agent.py
from typing import List, Dict
from pydantic import BaseModel
import os
from openai import OpenAI
from .tools import run_python, read_file, write_file

# Simple wrapper around OpenAI chat for "tool-aware" responses.
# This is a minimal agent loop: ask LL<PERSON>, parse for tool calls, run tool, send results back.

client = OpenAI(api_key=os.environ.get("OPENAI_API_KEY"))

class ChatMessage(BaseModel):
    role: str
    content: str


TOOL_DESCRIPTIONS = {
    "run_python": "Run Python code. Input = code string. Returns output.",
    "read_file": "Read a file under the project. Input = path. Returns file text or error.",
    "write_file": "Write a file under the project. Input = path and content separated by '|||'. Returns OK or error."
}


def call_llm(messages: List[Dict]) -> str:
    # Uses OpenAI Chat completions (Chat Completions or Responses depending on SDK)
    # using the OpenAI Python client (newer SDK). If your env uses openai.ChatCompletion,
    # adapt accordingly.
    resp = client.chat.completions.create(model="gpt-4", messages=messages, temperature=0)
    return resp.choices[0].message.content


# Very small parser convention for instructing the model to call tools:
# The model should output `TOOLCALL: tool_name` followed by a block with `INPUT:` and content.
# Example:
# TOOLCALL: run_python
# INPUT:
# print('hello')


def extract_toolcall(text: str):
    # crude parser — you can make this robust or use function-calling API
    if "TOOLCALL:" not in text:
        return None
    try:
        after = text.split("TOOLCALL:", 1)[1].strip()
        tool_name = after.splitlines()[0].strip()
        input_block = "\n".join(after.splitlines()[1:]).strip()
        if input_block.startswith("INPUT:"):
            input_val = input_block.split("INPUT:", 1)[1].strip()
        else:
            input_val = input_block
        return tool_name, input_val
    except Exception:
        return None


def agent_step(system_prompt: str, history: List[Dict], user_input: str) -> str:
    # Build messages
    messages = [
        {"role": "system", "content": system_prompt},
    ] + history + [{"role": "user", "content": user_input}]

    llm_out = call_llm(messages)

    tc = extract_toolcall(llm_out)
    if not tc:
        return llm_out

    tool, inp = tc
    if tool == "run_python":
        result = run_python(inp)
    elif tool == "read_file":
        result = read_file(inp)
    elif tool == "write_file":
        # expect path ||| content
        if "|||" in inp:
            path, content = inp.split("|||", 1)
            result = write_file(path.strip(), content)
        else:
            result = "ERROR: write_file expects 'path ||| content'"
    else:
        result = f"ERROR: unknown tool {tool}"

    # Continue conversation by sending tool result back to LLM for final answer
    messages.append({"role": "assistant", "content": llm_out})
    messages.append({"role": "system", "content": f"Tool result for {tool}:\n{result}"})
    final = call_llm(messages)
    return final
