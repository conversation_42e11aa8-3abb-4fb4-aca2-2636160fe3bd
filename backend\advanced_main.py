# backend/advanced_main.py
"""
Advanced FastAPI application that integrates all sophisticated agent capabilities.
This creates an AI assistant that works exactly like advanced AI assistants.
"""

import os
import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime
from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn

from .advanced_agent import AdvancedAgent, ConversationContext
from .response_generator import IntelligentResponseGenerator, ResponseContext

app = FastAPI(
    title="Advanced Augmented Agent",
    description="Sophisticated AI programming assistant with advanced reasoning capabilities",
    version="2.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global instances
agent = None
response_generator = IntelligentResponseGenerator()
active_sessions: Dict[str, ConversationContext] = {}

class ChatRequest(BaseModel):
    messages: List[Dict[str, str]]
    input: str
    session_id: Optional[str] = None
    preferences: Optional[Dict[str, Any]] = None

class ChatResponse(BaseModel):
    reply: str
    session_id: str
    context: Dict[str, Any]
    reasoning: Optional[Dict[str, Any]] = None

class AnalysisRequest(BaseModel):
    code: Optional[str] = None
    file_path: Optional[str] = None
    analysis_type: str = "full"

class AnalysisResponse(BaseModel):
    success: bool
    analysis: Dict[str, Any]
    suggestions: List[str]

@app.on_event("startup")
async def startup_event():
    """Initialize the advanced agent on startup."""
    global agent
    
    # Get OpenAI API key from environment
    api_key = os.environ.get("OPENAI_API_KEY")
    
    # Initialize the advanced agent
    agent = AdvancedAgent(api_key=api_key)
    
    print("🚀 Advanced Augmented Agent started successfully!")
    print(f"📡 API available at: http://localhost:8000")
    print(f"📚 Documentation at: http://localhost:8000/docs")
    print(f"🤖 Agent mode: {'OpenAI-powered' if api_key else 'Demo mode'}")

@app.get("/")
async def root():
    """Root endpoint with agent information."""
    return {
        "name": "Advanced Augmented Agent",
        "version": "2.0.0",
        "description": "Sophisticated AI programming assistant",
        "capabilities": [
            "Advanced code analysis and generation",
            "Intelligent debugging and problem solving",
            "Sophisticated reasoning and explanation",
            "Context-aware conversation management",
            "File operations and project management",
            "Testing and process management"
        ],
        "status": "ready",
        "timestamp": datetime.now().isoformat()
    }

@app.post("/chat", response_model=ChatResponse)
async def chat(request: ChatRequest):
    """Advanced chat endpoint with sophisticated reasoning."""
    try:
        # Get or create session
        session_id = request.session_id or f"session_{datetime.now().timestamp()}"
        
        if session_id not in active_sessions:
            active_sessions[session_id] = ConversationContext(
                messages=request.messages,
                project_context={},
                active_files=[],
                recent_operations=[],
                user_preferences=request.preferences or {},
                session_id=session_id
            )
        else:
            # Update existing session
            context = active_sessions[session_id]
            context.messages.extend(request.messages)
            if request.preferences:
                context.user_preferences.update(request.preferences)
        
        context = active_sessions[session_id]
        
        # Process the request with advanced reasoning
        response = await agent.process_request(request.input)
        
        # Update context with the interaction
        context.messages.append({"role": "user", "content": request.input})
        context.messages.append({"role": "assistant", "content": response})
        context.recent_operations.append(f"Chat: {request.input[:50]}...")
        
        # Get reasoning information if available
        reasoning_info = None
        if hasattr(agent, 'last_analysis'):
            reasoning_info = agent.last_analysis
        
        return ChatResponse(
            reply=response,
            session_id=session_id,
            context={
                "message_count": len(context.messages),
                "recent_operations": context.recent_operations[-5:],
                "active_files": context.active_files,
                "session_duration": datetime.now().isoformat()
            },
            reasoning=reasoning_info
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error processing chat: {str(e)}")

@app.post("/analyze", response_model=AnalysisResponse)
async def analyze_code(request: AnalysisRequest):
    """Advanced code analysis endpoint."""
    try:
        from .advanced_tools import CodeAnalyzer, analyze_codebase
        
        if request.code:
            # Analyze provided code
            analysis = CodeAnalyzer.analyze_python_code(request.code)
            suggestions = _generate_code_suggestions(analysis)
            
            return AnalysisResponse(
                success=True,
                analysis=analysis,
                suggestions=suggestions
            )
        
        elif request.file_path:
            # Analyze specific file
            from .advanced_tools import read_file_advanced
            file_result = await read_file_advanced(request.file_path)
            
            if file_result["success"]:
                analysis = file_result.get("analysis", {})
                suggestions = _generate_file_suggestions(analysis)
                
                return AnalysisResponse(
                    success=True,
                    analysis=analysis,
                    suggestions=suggestions
                )
            else:
                return AnalysisResponse(
                    success=False,
                    analysis={"error": file_result["error"]},
                    suggestions=[]
                )
        
        else:
            # Analyze entire codebase
            codebase_result = await analyze_codebase()
            
            if codebase_result["success"]:
                analysis = codebase_result["analysis"]
                suggestions = _generate_codebase_suggestions(analysis)
                
                return AnalysisResponse(
                    success=True,
                    analysis=analysis,
                    suggestions=suggestions
                )
            else:
                return AnalysisResponse(
                    success=False,
                    analysis={"error": codebase_result["error"]},
                    suggestions=[]
                )
                
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error analyzing code: {str(e)}")

@app.post("/execute")
async def execute_code(code: str, timeout: int = 10):
    """Execute Python code with advanced analysis."""
    try:
        from .advanced_tools import run_python_advanced
        
        result = await run_python_advanced(code, timeout=timeout)
        
        return {
            "success": result["success"],
            "output": result.get("output", ""),
            "error": result.get("error"),
            "execution_time": result.get("execution_time", 0),
            "analysis": result.get("analysis", {}),
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error executing code: {str(e)}")

@app.get("/sessions")
async def list_sessions():
    """List active conversation sessions."""
    return {
        "active_sessions": len(active_sessions),
        "sessions": [
            {
                "session_id": session_id,
                "message_count": len(context.messages),
                "last_activity": context.messages[-1]["content"][:50] + "..." if context.messages else "No messages",
                "recent_operations": len(context.recent_operations)
            }
            for session_id, context in active_sessions.items()
        ]
    }

@app.delete("/sessions/{session_id}")
async def clear_session(session_id: str):
    """Clear a specific conversation session."""
    if session_id in active_sessions:
        del active_sessions[session_id]
        return {"message": f"Session {session_id} cleared successfully"}
    else:
        raise HTTPException(status_code=404, detail="Session not found")

@app.get("/capabilities")
async def get_capabilities():
    """Get detailed information about agent capabilities."""
    return {
        "reasoning": {
            "description": "Advanced reasoning engine with multi-step problem solving",
            "features": ["Intent classification", "Complexity assessment", "Tool selection", "Reasoning chains"]
        },
        "tools": {
            "description": "Comprehensive tool system for development tasks",
            "available_tools": [
                "run_python_advanced", "read_file_advanced", "write_file_advanced",
                "analyze_codebase", "web_search_simulation", "run_tests", "manage_processes"
            ]
        },
        "response_generation": {
            "description": "Intelligent response formatting with context awareness",
            "features": ["Code highlighting", "Execution results", "Analysis summaries", "Step-by-step explanations"]
        },
        "context_management": {
            "description": "Sophisticated conversation and project context tracking",
            "features": ["Session management", "File tracking", "Operation history", "User preferences"]
        }
    }

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "agent_ready": agent is not None,
        "active_sessions": len(active_sessions),
        "version": "2.0.0"
    }

def _generate_code_suggestions(analysis: Dict[str, Any]) -> List[str]:
    """Generate suggestions based on code analysis."""
    suggestions = []
    
    if not analysis.get("syntax_valid", True):
        suggestions.append("Fix syntax errors before proceeding")
        return suggestions
    
    functions = analysis.get("functions", [])
    classes = analysis.get("classes", [])
    
    if len(functions) == 0 and len(classes) == 0:
        suggestions.append("Consider organizing code into functions for better modularity")
    
    if analysis.get("lines_of_code", 0) > 100:
        suggestions.append("Consider breaking down large code blocks into smaller functions")
    
    for func in functions:
        if not func.get("docstring"):
            suggestions.append(f"Add docstring to function '{func['name']}'")
    
    for cls in classes:
        if not cls.get("docstring"):
            suggestions.append(f"Add docstring to class '{cls['name']}'")
    
    if not suggestions:
        suggestions.append("Code looks good! Consider adding unit tests.")
    
    return suggestions

def _generate_file_suggestions(analysis: Dict[str, Any]) -> List[str]:
    """Generate suggestions based on file analysis."""
    suggestions = []
    
    if analysis.get("size", 0) > 10000:  # 10KB
        suggestions.append("Large file detected - consider splitting into modules")
    
    if analysis.get("lines", 0) > 500:
        suggestions.append("File has many lines - consider refactoring")
    
    return suggestions

def _generate_codebase_suggestions(analysis: Dict[str, Any]) -> List[str]:
    """Generate suggestions based on codebase analysis."""
    suggestions = []
    
    if analysis.get("total_files", 0) > 50:
        suggestions.append("Large codebase - consider adding documentation")
    
    python_analysis = analysis.get("python_analysis", {})
    if python_analysis.get("total_functions", 0) > 100:
        suggestions.append("Many functions detected - consider organizing into modules")
    
    if len(analysis.get("issues", [])) > 0:
        suggestions.append("Fix syntax errors and issues found in analysis")
    
    return suggestions

if __name__ == "__main__":
    uvicorn.run(
        "advanced_main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
