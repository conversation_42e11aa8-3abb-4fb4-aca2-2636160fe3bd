// frontend/src/App.jsx
import React, { useState } from 'react';

export default function App(){
  const [input, setInput] = useState('');
  const [log, setLog] = useState([]);

  async function send(){
    const history = log.map(item => ({role: item.role, content: item.content}));
    const res = await fetch('http://localhost:8000/chat', {
      method: 'POST',
      headers: {'Content-Type': 'application/json'},
      body: JSON.stringify({messages: history, input})
    });
    const data = await res.json();
    setLog(l => [...l, {role: 'user', content: input}, {role: 'assistant', content: data.reply}]);
    setInput('');
  }

  return (
    <div style={{maxWidth:800, margin:'0 auto'}}>
      <h2>Augmented Agent Chat</h2>
      <div style={{whiteSpace:'pre-wrap', background:'#f6f6f6', padding:10, minHeight:200}}>
        {log.map((m,i)=> <div key={i}><b>{m.role}:</b> {m.content}</div>)}
      </div>
      <textarea value={input} onChange={e=>setInput(e.target.value)} rows={4} style={{width:'100%'}} />
      <button onClick={send}>Send</button>
    </div>
  )
}
