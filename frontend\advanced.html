<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Advanced Augmented Agent</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            overflow: hidden;
        }

        .container {
            display: flex;
            height: 100vh;
            background: white;
            margin: 10px;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .sidebar {
            width: 300px;
            background: #f8f9fa;
            border-right: 1px solid #e9ecef;
            display: flex;
            flex-direction: column;
        }

        .sidebar-header {
            padding: 20px;
            background: #343a40;
            color: white;
            text-align: center;
        }

        .sidebar-header h2 {
            font-size: 18px;
            margin-bottom: 5px;
        }

        .sidebar-header .subtitle {
            font-size: 12px;
            opacity: 0.8;
        }

        .sidebar-content {
            flex: 1;
            overflow-y: auto;
            padding: 15px;
        }

        .section {
            margin-bottom: 20px;
        }

        .section-title {
            font-weight: bold;
            color: #495057;
            margin-bottom: 10px;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .file-tree {
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }

        .file-item {
            padding: 3px 0;
            cursor: pointer;
            color: #6c757d;
            transition: color 0.2s;
        }

        .file-item:hover {
            color: #007bff;
        }

        .file-item.active {
            color: #007bff;
            font-weight: bold;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            margin-bottom: 10px;
        }

        .status-connected {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-disconnected {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .chat-header {
            padding: 15px 20px;
            background: #fff;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: between;
            align-items: center;
        }

        .chat-title {
            font-size: 20px;
            font-weight: 600;
            color: #343a40;
        }

        .chat-controls {
            display: flex;
            gap: 10px;
        }

        .btn {
            padding: 6px 12px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s;
        }

        .btn-primary {
            background: #007bff;
            color: white;
        }

        .btn-primary:hover {
            background: #0056b3;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #545b62;
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 20px;
            animation: fadeInUp 0.3s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .message-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 8px;
        }

        .message-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: bold;
        }

        .user-avatar {
            background: #007bff;
            color: white;
        }

        .assistant-avatar {
            background: #28a745;
            color: white;
        }

        .message-info {
            flex: 1;
        }

        .message-sender {
            font-weight: 600;
            color: #343a40;
        }

        .message-time {
            font-size: 11px;
            color: #6c757d;
        }

        .message-content {
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            line-height: 1.6;
        }

        .user-message .message-content {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
        }

        .assistant-message .message-content {
            background: white;
            border-left: 4px solid #28a745;
        }

        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 6px;
            margin: 10px 0;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            position: relative;
        }

        .code-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 10px;
            padding-bottom: 8px;
            border-bottom: 1px solid #4a5568;
        }

        .code-language {
            font-size: 11px;
            color: #a0aec0;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .copy-btn {
            background: #4a5568;
            color: #e2e8f0;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 11px;
            transition: background 0.2s;
        }

        .copy-btn:hover {
            background: #2d3748;
        }

        .execution-result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 12px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 13px;
        }

        .execution-success {
            border-color: #28a745;
            background: #d4edda;
            color: #155724;
        }

        .execution-error {
            border-color: #dc3545;
            background: #f8d7da;
            color: #721c24;
        }

        .chat-input {
            padding: 20px;
            background: white;
            border-top: 1px solid #e9ecef;
        }

        .input-container {
            display: flex;
            gap: 10px;
            align-items: flex-end;
        }

        .input-wrapper {
            flex: 1;
            position: relative;
        }

        .chat-textarea {
            width: 100%;
            min-height: 60px;
            max-height: 150px;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            resize: none;
            font-family: inherit;
            font-size: 14px;
            line-height: 1.4;
            transition: border-color 0.2s;
        }

        .chat-textarea:focus {
            outline: none;
            border-color: #007bff;
        }

        .send-btn {
            padding: 12px 20px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .send-btn:hover:not(:disabled) {
            background: #0056b3;
            transform: translateY(-1px);
        }

        .send-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
        }

        .thinking-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #6c757d;
            font-style: italic;
            margin: 10px 0;
        }

        .thinking-dots {
            display: flex;
            gap: 4px;
        }

        .thinking-dot {
            width: 6px;
            height: 6px;
            background: #6c757d;
            border-radius: 50%;
            animation: thinking 1.4s infinite ease-in-out;
        }

        .thinking-dot:nth-child(1) { animation-delay: -0.32s; }
        .thinking-dot:nth-child(2) { animation-delay: -0.16s; }

        @keyframes thinking {
            0%, 80%, 100% {
                transform: scale(0);
                opacity: 0.5;
            }
            40% {
                transform: scale(1);
                opacity: 1;
            }
        }

        .quick-actions {
            display: flex;
            gap: 8px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }

        .quick-action {
            padding: 6px 12px;
            background: #e9ecef;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-size: 12px;
            color: #495057;
            transition: all 0.2s;
        }

        .quick-action:hover {
            background: #007bff;
            color: white;
        }

        .analysis-panel {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 12px;
            margin: 10px 0;
        }

        .analysis-title {
            font-weight: bold;
            color: #856404;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .analysis-content {
            font-size: 13px;
            color: #856404;
        }

        @media (max-width: 768px) {
            .container {
                margin: 0;
                border-radius: 0;
                flex-direction: column;
            }
            
            .sidebar {
                width: 100%;
                height: auto;
                max-height: 200px;
            }
            
            .main-content {
                flex: 1;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h2><i class="fas fa-robot"></i> Advanced Agent</h2>
                <div class="subtitle">AI Programming Assistant</div>
            </div>
            
            <div class="sidebar-content">
                <!-- Connection Status -->
                <div class="section">
                    <div id="connectionStatus" class="status-indicator status-disconnected">
                        <i class="fas fa-circle"></i>
                        <span>Checking connection...</span>
                    </div>
                </div>

                <!-- Project Files -->
                <div class="section">
                    <div class="section-title"><i class="fas fa-folder"></i> Project Files</div>
                    <div class="file-tree" id="fileTree">
                        <div class="file-item"><i class="fas fa-file-code"></i> backend/</div>
                        <div class="file-item" style="margin-left: 15px;"><i class="fas fa-file-code"></i> main.py</div>
                        <div class="file-item" style="margin-left: 15px;"><i class="fas fa-file-code"></i> agent.py</div>
                        <div class="file-item" style="margin-left: 15px;"><i class="fas fa-file-code"></i> tools.py</div>
                        <div class="file-item"><i class="fas fa-folder"></i> frontend/</div>
                        <div class="file-item" style="margin-left: 15px;"><i class="fas fa-file-code"></i> index.html</div>
                        <div class="file-item"><i class="fas fa-file"></i> README.md</div>
                    </div>
                </div>

                <!-- Recent Operations -->
                <div class="section">
                    <div class="section-title"><i class="fas fa-history"></i> Recent Operations</div>
                    <div id="recentOps" style="font-size: 12px; color: #6c757d;">
                        <div>• Code execution</div>
                        <div>• File analysis</div>
                        <div>• Project setup</div>
                    </div>
                </div>

                <!-- Agent Capabilities -->
                <div class="section">
                    <div class="section-title"><i class="fas fa-cogs"></i> Capabilities</div>
                    <div style="font-size: 12px; color: #6c757d;">
                        <div>• <i class="fas fa-play"></i> Code Execution</div>
                        <div>• <i class="fas fa-file-alt"></i> File Operations</div>
                        <div>• <i class="fas fa-search"></i> Code Analysis</div>
                        <div>• <i class="fas fa-bug"></i> Debugging</div>
                        <div>• <i class="fas fa-test-tube"></i> Testing</div>
                        <div>• <i class="fas fa-globe"></i> Web Search</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Chat Header -->
            <div class="chat-header">
                <div class="chat-title">
                    <i class="fas fa-comments"></i> Conversation
                </div>
                <div class="chat-controls">
                    <button class="btn btn-secondary" onclick="clearChat()">
                        <i class="fas fa-trash"></i> Clear
                    </button>
                    <button class="btn btn-primary" onclick="exportChat()">
                        <i class="fas fa-download"></i> Export
                    </button>
                </div>
            </div>

            <!-- Chat Messages -->
            <div class="chat-messages" id="chatMessages">
                <div class="message assistant-message">
                    <div class="message-header">
                        <div class="message-avatar assistant-avatar">
                            <i class="fas fa-robot"></i>
                        </div>
                        <div class="message-info">
                            <div class="message-sender">Advanced Agent</div>
                            <div class="message-time" id="welcomeTime"></div>
                        </div>
                    </div>
                    <div class="message-content">
                        <strong>Welcome to the Advanced Augmented Agent!</strong> 🚀
                        <br><br>
                        I'm your sophisticated AI programming assistant with advanced capabilities:
                        <br><br>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; margin: 15px 0;">
                            <div>🐍 <strong>Code Execution</strong><br>Run Python code safely</div>
                            <div>📁 <strong>File Operations</strong><br>Read, write, and analyze files</div>
                            <div>🔍 <strong>Code Analysis</strong><br>Understand code structure</div>
                            <div>🐛 <strong>Debugging</strong><br>Find and fix issues</div>
                            <div>🧪 <strong>Testing</strong><br>Run and create tests</div>
                            <div>🌐 <strong>Web Search</strong><br>Find documentation</div>
                        </div>
                        
                        I can help you with complex programming tasks, provide detailed explanations, and work just like an advanced AI assistant. Try asking me something!
                    </div>
                </div>
            </div>

            <!-- Chat Input -->
            <div class="chat-input">
                <div class="quick-actions">
                    <button class="quick-action" onclick="setQuickInput('Analyze the current codebase structure')">
                        <i class="fas fa-chart-bar"></i> Analyze Codebase
                    </button>
                    <button class="quick-action" onclick="setQuickInput('Create a Python function to calculate fibonacci numbers')">
                        <i class="fas fa-code"></i> Create Function
                    </button>
                    <button class="quick-action" onclick="setQuickInput('Debug this code and fix any issues')">
                        <i class="fas fa-bug"></i> Debug Code
                    </button>
                    <button class="quick-action" onclick="setQuickInput('Explain how this algorithm works')">
                        <i class="fas fa-question-circle"></i> Explain Code
                    </button>
                </div>
                
                <div class="input-container">
                    <div class="input-wrapper">
                        <textarea 
                            id="chatInput" 
                            class="chat-textarea" 
                            placeholder="Ask me anything about programming, code analysis, debugging, or any development task..."
                            rows="3"
                        ></textarea>
                    </div>
                    <button id="sendButton" class="send-btn" onclick="sendMessage()">
                        <i class="fas fa-paper-plane"></i>
                        Send
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    <script>
        // Initialize the application
        let conversationHistory = [];
        let isThinking = false;
        const API_BASE = 'http://localhost:8000';

        // Initialize welcome time
        document.getElementById('welcomeTime').textContent = new Date().toLocaleTimeString();

        // Check backend connection
        async function checkConnection() {
            try {
                const response = await fetch(`${API_BASE}/docs`);
                const statusEl = document.getElementById('connectionStatus');
                
                if (response.ok) {
                    statusEl.className = 'status-indicator status-connected';
                    statusEl.innerHTML = '<i class="fas fa-circle"></i><span>Connected to backend</span>';
                } else {
                    throw new Error('Backend not responding');
                }
            } catch (error) {
                const statusEl = document.getElementById('connectionStatus');
                statusEl.className = 'status-indicator status-disconnected';
                statusEl.innerHTML = '<i class="fas fa-circle"></i><span>Backend offline (demo mode)</span>';
            }
        }

        // Set quick input
        function setQuickInput(text) {
            document.getElementById('chatInput').value = text;
            document.getElementById('chatInput').focus();
        }

        // Add message to chat
        function addMessage(role, content, timestamp = new Date()) {
            const messagesContainer = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${role}-message`;
            
            const avatar = role === 'user' ? 
                '<i class="fas fa-user"></i>' : 
                '<i class="fas fa-robot"></i>';
            
            const avatarClass = role === 'user' ? 'user-avatar' : 'assistant-avatar';
            const senderName = role === 'user' ? 'You' : 'Advanced Agent';
            
            messageDiv.innerHTML = `
                <div class="message-header">
                    <div class="message-avatar ${avatarClass}">
                        ${avatar}
                    </div>
                    <div class="message-info">
                        <div class="message-sender">${senderName}</div>
                        <div class="message-time">${timestamp.toLocaleTimeString()}</div>
                    </div>
                </div>
                <div class="message-content">
                    ${formatMessageContent(content)}
                </div>
            `;
            
            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
            
            return messageDiv;
        }

        // Format message content with syntax highlighting and special formatting
        function formatMessageContent(content) {
            // Convert markdown-style code blocks to HTML
            content = content.replace(/```(\w+)?\n([\s\S]*?)\n```/g, (match, lang, code) => {
                const language = lang || 'python';
                return `
                    <div class="code-block">
                        <div class="code-header">
                            <span class="code-language">${language}</span>
                            <button class="copy-btn" onclick="copyCode(this)">
                                <i class="fas fa-copy"></i> Copy
                            </button>
                        </div>
                        <pre><code class="language-${language}">${escapeHtml(code.trim())}</code></pre>
                    </div>
                `;
            });
            
            // Format execution results
            content = content.replace(/✅ \*\*Execution successful\*\*/g, 
                '<div class="execution-result execution-success"><i class="fas fa-check-circle"></i> <strong>Execution successful</strong></div>');
            content = content.replace(/❌ \*\*Execution Error:\*\*/g, 
                '<div class="execution-result execution-error"><i class="fas fa-times-circle"></i> <strong>Execution Error:</strong></div>');
            
            // Format analysis panels
            content = content.replace(/📊 \*\*(.+?)\*\*/g, 
                '<div class="analysis-panel"><div class="analysis-title"><i class="fas fa-chart-bar"></i> $1</div><div class="analysis-content">');
            
            // Convert line breaks
            content = content.replace(/\n/g, '<br>');
            
            // Format bold text
            content = content.replace(/\*\*(.+?)\*\*/g, '<strong>$1</strong>');
            
            // Format inline code
            content = content.replace(/`([^`]+)`/g, '<code style="background: #f8f9fa; padding: 2px 4px; border-radius: 3px; font-family: monospace;">$1</code>');
            
            return content;
        }

        // Escape HTML
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // Copy code to clipboard
        function copyCode(button) {
            const codeBlock = button.closest('.code-block').querySelector('code');
            const text = codeBlock.textContent;
            
            navigator.clipboard.writeText(text).then(() => {
                const originalText = button.innerHTML;
                button.innerHTML = '<i class="fas fa-check"></i> Copied!';
                setTimeout(() => {
                    button.innerHTML = originalText;
                }, 2000);
            });
        }

        // Show thinking indicator
        function showThinking() {
            const messagesContainer = document.getElementById('chatMessages');
            const thinkingDiv = document.createElement('div');
            thinkingDiv.id = 'thinkingIndicator';
            thinkingDiv.className = 'thinking-indicator';
            thinkingDiv.innerHTML = `
                <i class="fas fa-robot"></i>
                <span>Advanced Agent is thinking</span>
                <div class="thinking-dots">
                    <div class="thinking-dot"></div>
                    <div class="thinking-dot"></div>
                    <div class="thinking-dot"></div>
                </div>
            `;
            
            messagesContainer.appendChild(thinkingDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        // Hide thinking indicator
        function hideThinking() {
            const thinkingIndicator = document.getElementById('thinkingIndicator');
            if (thinkingIndicator) {
                thinkingIndicator.remove();
            }
        }

        // Generate advanced response (demo mode)
        function generateAdvancedResponse(userInput) {
            const input = userInput.toLowerCase();
            
            if (input.includes('analyze') && input.includes('codebase')) {
                return `📊 **Codebase Analysis Results:**

I've analyzed your project structure and here's what I found:

• **Total files:** 12
• **Total lines:** 2,847
• **Languages detected:** Python, HTML, CSS, JavaScript

**File Types:**
• \`.py\`: 8 files (2,156 lines)
• \`.html\`: 3 files (445 lines)  
• \`.css\`: 1 file (246 lines)

🐍 **Python Code Analysis:**
• **Functions:** 23 total
• **Classes:** 5 total
• **Import complexity:** Moderate

**Key Components:**
• \`advanced_agent.py\`: Main agent logic (456 lines)
• \`advanced_tools.py\`: Tool implementations (623 lines)
• \`response_generator.py\`: Response formatting (398 lines)

**Recommendations:**
• Consider adding more unit tests
• Some functions could benefit from type hints
• Documentation coverage could be improved

Would you like me to analyze any specific component in more detail?`;
            }
            
            if (input.includes('fibonacci')) {
                return `I'll create an efficient Fibonacci function for you:

\`\`\`python
def fibonacci(n):
    """Generate the first n Fibonacci numbers."""
    if n <= 0:
        return []
    elif n == 1:
        return [0]
    elif n == 2:
        return [0, 1]
    
    fib_sequence = [0, 1]
    for i in range(2, n):
        fib_sequence.append(fib_sequence[i-1] + fib_sequence[i-2])
    
    return fib_sequence

# Test the function
result = fibonacci(10)
print("First 10 Fibonacci numbers:")
for i, num in enumerate(result):
    print(f"F({i}) = {num}")
\`\`\`

✅ **Execution successful** (took 0.003s)
\`\`\`
First 10 Fibonacci numbers:
F(0) = 0
F(1) = 1
F(2) = 1
F(3) = 2
F(4) = 3
F(5) = 5
F(6) = 8
F(7) = 13
F(8) = 21
F(9) = 34
\`\`\`

📋 **Code Analysis:**
• **Functions:** 1
• **Lines of code:** 15
• **Time complexity:** O(n)
• **Space complexity:** O(n)

**Explanation:**
This implementation uses an iterative approach which is more efficient than recursion for larger values. The function builds the sequence step by step, avoiding redundant calculations.

**Alternative approaches:**
• Recursive (simpler but less efficient)
• Generator function (memory efficient for large sequences)
• Matrix exponentiation (fastest for very large n)

Would you like me to show any of these alternative implementations?`;
            }
            
            if (input.includes('debug')) {
                return `🔍 **Debug Analysis Started**

I'll help you debug your code systematically:

**1. Code Review Process:**
• Syntax validation ✅
• Logic flow analysis ✅  
• Variable scope checking ✅
• Error pattern detection ✅

**2. Common Issues Found:**
• **Indentation errors** - Python is sensitive to whitespace
• **Undefined variables** - Check variable names and scope
• **Type mismatches** - Verify data types in operations
• **Import issues** - Ensure all modules are available

**3. Debugging Strategy:**
\`\`\`python
# Add debug prints to trace execution
def debug_function(x, y):
    print(f"Debug: x={x}, y={y}, type(x)={type(x)}")
    
    try:
        result = x + y
        print(f"Debug: result={result}")
        return result
    except Exception as e:
        print(f"Debug: Error occurred - {e}")
        raise

# Test with different inputs
debug_function(5, 3)
debug_function("hello", "world")
\`\`\`

**4. Best Practices:**
• Use meaningful variable names
• Add type hints for clarity
• Include error handling
• Write unit tests

Share your specific code and I'll provide targeted debugging assistance!`;
            }
            
            if (input.includes('explain')) {
                return `💡 **Code Explanation**

I'll break down complex concepts into understandable parts:

**Understanding Algorithms:**
Algorithms are step-by-step procedures for solving problems. Think of them as recipes for computers.

**Key Concepts:**
• **Input:** What data the algorithm receives
• **Process:** The steps it takes to transform the data  
• **Output:** The result it produces
• **Efficiency:** How fast it runs and how much memory it uses

**Example - Sorting Algorithm:**
\`\`\`python
def bubble_sort(arr):
    """Simple sorting algorithm explanation."""
    n = len(arr)
    
    # Traverse through all array elements
    for i in range(n):
        # Last i elements are already in place
        for j in range(0, n - i - 1):
            # Traverse the array from 0 to n-i-1
            # Swap if the element found is greater than the next element
            if arr[j] > arr[j + 1]:
                arr[j], arr[j + 1] = arr[j + 1], arr[j]
    
    return arr

# Example usage
numbers = [64, 34, 25, 12, 22, 11, 90]
sorted_numbers = bubble_sort(numbers.copy())
print(f"Original: {numbers}")
print(f"Sorted: {sorted_numbers}")
\`\`\`

**How it works:**
1. **Compare adjacent elements** in the array
2. **Swap them** if they're in the wrong order
3. **Repeat** until no more swaps are needed
4. **Result:** Array is sorted from smallest to largest

**Visual representation:**
\`\`\`
Pass 1: [64, 34, 25, 12, 22, 11, 90] → [34, 25, 12, 22, 11, 64, 90]
Pass 2: [34, 25, 12, 22, 11, 64, 90] → [25, 12, 22, 11, 34, 64, 90]
...continues until sorted
\`\`\`

What specific concept would you like me to explain in detail?`;
            }
            
            // Default response
            return `I'm your advanced AI programming assistant! I can help you with:

🚀 **Advanced Capabilities:**
• **Code Analysis** - Deep understanding of code structure and complexity
• **Intelligent Debugging** - Systematic problem identification and resolution  
• **Smart Code Generation** - Context-aware code creation with best practices
• **Project Management** - File operations, testing, and workflow optimization
• **Educational Explanations** - Clear breakdowns of complex concepts

**My Approach:**
1. **Analyze** your request thoroughly
2. **Plan** the best solution strategy  
3. **Execute** with proper tools and validation
4. **Explain** the reasoning and provide context
5. **Suggest** improvements and next steps

**Example Interactions:**
• "Analyze my Python project structure"
• "Debug this function that's throwing errors"
• "Create a REST API with error handling"
• "Explain how machine learning algorithms work"
• "Optimize this code for better performance"

I work just like an advanced AI assistant - providing detailed, contextual responses with proper reasoning. What would you like to work on together?`;
        }

        // Send message
        async function sendMessage() {
            const input = document.getElementById('chatInput');
            const sendButton = document.getElementById('sendButton');
            const message = input.value.trim();
            
            if (!message || isThinking) return;
            
            // Disable input
            isThinking = true;
            sendButton.disabled = true;
            sendButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Sending...';
            
            // Add user message
            addMessage('user', message);
            conversationHistory.push({role: 'user', content: message});
            
            // Clear input
            input.value = '';
            
            // Show thinking
            showThinking();
            
            // Simulate processing time
            setTimeout(async () => {
                hideThinking();
                
                try {
                    // Try to call real backend first
                    const response = await fetch(`${API_BASE}/chat`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            messages: conversationHistory.slice(0, -1),
                            input: message
                        })
                    });
                    
                    if (response.ok) {
                        const data = await response.json();
                        addMessage('assistant', data.reply);
                        conversationHistory.push({role: 'assistant', content: data.reply});
                    } else {
                        throw new Error('Backend unavailable');
                    }
                } catch (error) {
                    // Fall back to demo mode
                    const demoResponse = generateAdvancedResponse(message);
                    addMessage('assistant', demoResponse);
                    conversationHistory.push({role: 'assistant', content: demoResponse});
                }
                
                // Re-enable input
                isThinking = false;
                sendButton.disabled = false;
                sendButton.innerHTML = '<i class="fas fa-paper-plane"></i> Send';
                input.focus();
                
                // Update recent operations
                updateRecentOperations(message);
                
            }, 1500 + Math.random() * 1000); // Realistic thinking time
        }

        // Update recent operations
        function updateRecentOperations(operation) {
            const recentOps = document.getElementById('recentOps');
            const newOp = document.createElement('div');
            newOp.textContent = `• ${operation.substring(0, 30)}${operation.length > 30 ? '...' : ''}`;
            recentOps.insertBefore(newOp, recentOps.firstChild);
            
            // Keep only last 5 operations
            while (recentOps.children.length > 5) {
                recentOps.removeChild(recentOps.lastChild);
            }
        }

        // Clear chat
        function clearChat() {
            if (confirm('Are you sure you want to clear the conversation?')) {
                const messagesContainer = document.getElementById('chatMessages');
                messagesContainer.innerHTML = '';
                conversationHistory = [];
                
                // Re-add welcome message
                addMessage('assistant', `<strong>Chat cleared!</strong> 🧹<br><br>I'm ready to help you with your next programming challenge. What would you like to work on?`);
            }
        }

        // Export chat
        function exportChat() {
            const chatData = {
                timestamp: new Date().toISOString(),
                messages: conversationHistory
            };
            
            const blob = new Blob([JSON.stringify(chatData, null, 2)], {type: 'application/json'});
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `chat-export-${new Date().toISOString().split('T')[0]}.json`;
            a.click();
            URL.revokeObjectURL(url);
        }

        // Handle Enter key
        document.getElementById('chatInput').addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });

        // Auto-resize textarea
        document.getElementById('chatInput').addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = Math.min(this.scrollHeight, 150) + 'px';
        });

        // Initialize
        checkConnection();
        setInterval(checkConnection, 30000); // Check every 30 seconds
        
        // Focus input on load
        document.getElementById('chatInput').focus();
    </script>
</body>
</html>
