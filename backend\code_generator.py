# backend/code_generator.py
"""
Advanced Code Generation System
Intelligent code generation for components, APIs, database schemas, and complete applications
"""

import re
import json
import ast
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
from pathlib import Path

@dataclass
class CodeTemplate:
    """Template for code generation."""
    name: str
    language: str
    framework: str
    template: str
    variables: List[str]
    dependencies: List[str] = None

class IntelligentCodeGenerator:
    """Advanced code generator with AI-like intelligence."""
    
    def __init__(self):
        self.templates = self._load_templates()
        self.patterns = self._load_patterns()
        self.best_practices = self._load_best_practices()
    
    def _load_templates(self) -> Dict[str, CodeTemplate]:
        """Load code templates for different frameworks and patterns."""
        return {
            "react_crud_component": CodeTemplate(
                name="React CRUD Component",
                language="javascript",
                framework="react",
                template="""import React, {{ useState, useEffect }} from 'react';
import axios from 'axios';
import './{{component_name}}.css';

const {{component_name}} = () => {{
  const [{{entity_plural}}, set{{entity_name}}s] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [form, setForm] = useState({{{{initial_form}}}});
  const [editing, setEditing] = useState(null);

  useEffect(() => {{
    fetch{{entity_name}}s();
  }}, []);

  const fetch{{entity_name}}s = async () => {{
    try {{
      setLoading(true);
      const response = await axios.get('/api/{{api_endpoint}}');
      set{{entity_name}}s(response.data);
    }} catch (err) {{
      setError(err.message);
    }} finally {{
      setLoading(false);
    }}
  }};

  const handle{{entity_name}}Submit = async (e) => {{
    e.preventDefault();
    try {{
      if (editing) {{
        await axios.put(`/api/{{api_endpoint}}/${{editing.id}}`, form);
      }} else {{
        await axios.post('/api/{{api_endpoint}}', form);
      }}
      setForm({{{{initial_form}}}});
      setEditing(null);
      fetch{{entity_name}}s();
    }} catch (err) {{
      setError(err.message);
    }}
  }};

  const handle{{entity_name}}Delete = async (id) => {{
    if (window.confirm('Are you sure?')) {{
      try {{
        await axios.delete(`/api/{{api_endpoint}}/${{id}}`);
        fetch{{entity_name}}s();
      }} catch (err) {{
        setError(err.message);
      }}
    }}
  }};

  const handle{{entity_name}}Edit = ({{entity_lower}}) => {{
    setForm({{entity_lower}});
    setEditing({{entity_lower}});
  }};

  if (loading) return <div className="loading">Loading...</div>;
  if (error) return <div className="error">Error: {{error}}</div>;

  return (
    <div className="{{component_name_lower}}">
      <h2>{{entity_name}} Management</h2>
      
      <form onSubmit={{handle{{entity_name}}Submit}} className="{{entity_lower}}-form">
        {{form_fields}}
        <button type="submit">
          {{editing ? 'Update' : 'Create'}} {{entity_name}}
        </button>
        {{editing && (
          <button type="button" onClick={{() => {{ setEditing(null); setForm({{{{initial_form}}}}); }}}}>
            Cancel
          </button>
        )}}
      </form>

      <div className="{{entity_lower}}-list">
        {{{{entity_plural}}.map({{entity_lower}} => (
          <div key={{{{entity_lower}}.id}} className="{{entity_lower}}-item">
            {{display_fields}}
            <div className="actions">
              <button onClick={{() => handle{{entity_name}}Edit({{entity_lower}})}}>Edit</button>
              <button onClick={{() => handle{{entity_name}}Delete({{entity_lower}}.id)}}>Delete</button>
            </div>
          </div>
        ))}}
      </div>
    </div>
  );
}};

export default {{component_name}};""",
                variables=["component_name", "entity_name", "entity_plural", "entity_lower", "api_endpoint", "initial_form", "form_fields", "display_fields"],
                dependencies=["react", "axios"]
            ),
            
            "fastapi_crud_router": CodeTemplate(
                name="FastAPI CRUD Router",
                language="python",
                framework="fastapi",
                template="""from fastapi import APIRouter, HTTPException, Depends
from sqlalchemy.orm import Session
from typing import List
from ..database import get_db
from ..models.{{model_file}} import {{model_name}}
from ..schemas.{{schema_file}} import {{schema_name}}, {{schema_name}}Create, {{schema_name}}Update

router = APIRouter(
    prefix="/{{api_prefix}}",
    tags=["{{tag_name}}"]
)

@router.get("/", response_model=List[{{schema_name}}])
async def get_{{entity_plural}}(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    \"\"\"Get all {{entity_plural}}.\"\"\"
    {{entity_plural}} = db.query({{model_name}}).offset(skip).limit(limit).all()
    return {{entity_plural}}

@router.get("/{{{{id}}}}", response_model={{schema_name}})
async def get_{{entity_lower}}(
    id: int,
    db: Session = Depends(get_db)
):
    \"\"\"Get a specific {{entity_lower}} by ID.\"\"\"
    {{entity_lower}} = db.query({{model_name}}).filter({{model_name}}.id == id).first()
    if not {{entity_lower}}:
        raise HTTPException(status_code=404, detail="{{entity_name}} not found")
    return {{entity_lower}}

@router.post("/", response_model={{schema_name}})
async def create_{{entity_lower}}(
    {{entity_lower}}: {{schema_name}}Create,
    db: Session = Depends(get_db)
):
    \"\"\"Create a new {{entity_lower}}.\"\"\"
    db_{{entity_lower}} = {{model_name}}(**{{entity_lower}}.dict())
    db.add(db_{{entity_lower}})
    db.commit()
    db.refresh(db_{{entity_lower}})
    return db_{{entity_lower}}

@router.put("/{{{{id}}}}", response_model={{schema_name}})
async def update_{{entity_lower}}(
    id: int,
    {{entity_lower}}_update: {{schema_name}}Update,
    db: Session = Depends(get_db)
):
    \"\"\"Update an existing {{entity_lower}}.\"\"\"
    {{entity_lower}} = db.query({{model_name}}).filter({{model_name}}.id == id).first()
    if not {{entity_lower}}:
        raise HTTPException(status_code=404, detail="{{entity_name}} not found")
    
    update_data = {{entity_lower}}_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr({{entity_lower}}, field, value)
    
    db.commit()
    db.refresh({{entity_lower}})
    return {{entity_lower}}

@router.delete("/{{{{id}}}}")
async def delete_{{entity_lower}}(
    id: int,
    db: Session = Depends(get_db)
):
    \"\"\"Delete a {{entity_lower}}.\"\"\"
    {{entity_lower}} = db.query({{model_name}}).filter({{model_name}}.id == id).first()
    if not {{entity_lower}}:
        raise HTTPException(status_code=404, detail="{{entity_name}} not found")
    
    db.delete({{entity_lower}})
    db.commit()
    return {{"message": "{{entity_name}} deleted successfully"}}""",
                variables=["model_name", "schema_name", "entity_name", "entity_lower", "entity_plural", "api_prefix", "tag_name", "model_file", "schema_file"],
                dependencies=["fastapi", "sqlalchemy"]
            ),
            
            "sqlalchemy_model": CodeTemplate(
                name="SQLAlchemy Model",
                language="python",
                framework="sqlalchemy",
                template="""from sqlalchemy import Column, Integer, String, DateTime, Text, Boolean, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime

Base = declarative_base()

class {{model_name}}(Base):
    \"\"\"{{model_description}}\"\"\"
    __tablename__ = "{{table_name}}"
    
    id = Column(Integer, primary_key=True, index=True)
    {{model_fields}}
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    {{relationships}}
    
    def __repr__(self):
        return f"<{{model_name}}(id={{self.id}}, {{repr_field}}={{self.{{repr_field}}}})>"
    
    def to_dict(self):
        return {{
            "id": self.id,
            {{to_dict_fields}}
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }}""",
                variables=["model_name", "model_description", "table_name", "model_fields", "relationships", "repr_field", "to_dict_fields"],
                dependencies=["sqlalchemy"]
            ),
            
            "pydantic_schema": CodeTemplate(
                name="Pydantic Schema",
                language="python",
                framework="pydantic",
                template="""from pydantic import BaseModel, validator
from typing import Optional
from datetime import datetime

class {{schema_name}}Base(BaseModel):
    \"\"\"Base schema for {{entity_name}}.\"\"\"
    {{base_fields}}

class {{schema_name}}Create({{schema_name}}Base):
    \"\"\"Schema for creating {{entity_name}}.\"\"\"
    {{create_validators}}

class {{schema_name}}Update(BaseModel):
    \"\"\"Schema for updating {{entity_name}}.\"\"\"
    {{update_fields}}

class {{schema_name}}({{schema_name}}Base):
    \"\"\"Schema for {{entity_name}} response.\"\"\"
    id: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        orm_mode = True""",
                variables=["schema_name", "entity_name", "base_fields", "create_validators", "update_fields"],
                dependencies=["pydantic"]
            )
        }
    
    def _load_patterns(self) -> Dict[str, Any]:
        """Load common coding patterns and structures."""
        return {
            "mvc": {
                "description": "Model-View-Controller pattern",
                "structure": ["models", "views", "controllers"],
                "files": ["model.py", "view.py", "controller.py"]
            },
            "repository": {
                "description": "Repository pattern for data access",
                "structure": ["repositories", "interfaces"],
                "files": ["repository.py", "interface.py"]
            },
            "service_layer": {
                "description": "Service layer pattern",
                "structure": ["services", "dto"],
                "files": ["service.py", "dto.py"]
            }
        }
    
    def _load_best_practices(self) -> Dict[str, List[str]]:
        """Load coding best practices for different languages/frameworks."""
        return {
            "react": [
                "Use functional components with hooks",
                "Implement proper error boundaries",
                "Use TypeScript for type safety",
                "Follow component composition patterns",
                "Implement proper state management",
                "Use React.memo for performance optimization"
            ],
            "fastapi": [
                "Use dependency injection",
                "Implement proper error handling",
                "Use Pydantic models for validation",
                "Follow RESTful API conventions",
                "Implement proper authentication",
                "Use async/await for I/O operations"
            ],
            "python": [
                "Follow PEP 8 style guide",
                "Use type hints",
                "Implement proper exception handling",
                "Write comprehensive docstrings",
                "Use virtual environments",
                "Follow SOLID principles"
            ]
        }
    
    async def generate_crud_system(self, entity_config: Dict[str, Any]) -> Dict[str, Any]:
        """Generate a complete CRUD system for an entity."""
        try:
            entity_name = entity_config["name"]
            fields = entity_config["fields"]
            framework = entity_config.get("framework", "react_fastapi")
            
            generated_files = {}
            
            if framework in ["react", "react_fastapi"]:
                # Generate React component
                react_component = await self._generate_react_crud_component(entity_name, fields)
                generated_files[f"{entity_name}Manager.js"] = react_component
                
                # Generate CSS
                css_content = await self._generate_component_css(entity_name)
                generated_files[f"{entity_name}Manager.css"] = css_content
            
            if framework in ["fastapi", "react_fastapi"]:
                # Generate FastAPI router
                api_router = await self._generate_fastapi_crud_router(entity_name, fields)
                generated_files[f"{entity_name.lower()}_router.py"] = api_router
                
                # Generate SQLAlchemy model
                model = await self._generate_sqlalchemy_model(entity_name, fields)
                generated_files[f"{entity_name.lower()}_model.py"] = model
                
                # Generate Pydantic schemas
                schemas = await self._generate_pydantic_schemas(entity_name, fields)
                generated_files[f"{entity_name.lower()}_schemas.py"] = schemas
            
            return {
                "success": True,
                "files": generated_files,
                "entity": entity_name,
                "framework": framework
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"Failed to generate CRUD system: {str(e)}"
            }
    
    async def _generate_react_crud_component(self, entity_name: str, fields: List[Dict[str, Any]]) -> str:
        """Generate React CRUD component."""
        template = self.templates["react_crud_component"]
        
        # Prepare template variables
        entity_lower = entity_name.lower()
        entity_plural = entity_name.lower() + "s"  # Simple pluralization
        component_name = f"{entity_name}Manager"
        component_name_lower = component_name.lower()
        api_endpoint = entity_plural
        
        # Generate initial form state
        initial_form = {}
        form_fields = []
        display_fields = []
        
        for field in fields:
            field_name = field["name"]
            field_type = field["type"]
            
            # Initial form value
            if field_type == "string":
                initial_form[field_name] = "''"
            elif field_type == "number":
                initial_form[field_name] = "0"
            elif field_type == "boolean":
                initial_form[field_name] = "false"
            else:
                initial_form[field_name] = "''"
            
            # Form field JSX
            if field_type == "boolean":
                form_fields.append(f"""        <label>
          <input
            type="checkbox"
            checked={{form.{field_name}}}
            onChange={{(e) => setForm({{...form, {field_name}: e.target.checked}})}}
          />
          {field_name.title()}
        </label>""")
            else:
                input_type = "number" if field_type == "number" else "text"
                form_fields.append(f"""        <label>
          {field_name.title()}:
          <input
            type="{input_type}"
            value={{form.{field_name}}}
            onChange={{(e) => setForm({{...form, {field_name}: e.target.value}})}}
            required
          />
        </label>""")
            
            # Display field JSX
            display_fields.append(f"            <p><strong>{field_name.title()}:</strong> {{{entity_lower}.{field_name}}}</p>")
        
        # Replace template variables
        content = template.template
        replacements = {
            "component_name": component_name,
            "component_name_lower": component_name_lower,
            "entity_name": entity_name,
            "entity_lower": entity_lower,
            "entity_plural": entity_plural,
            "api_endpoint": api_endpoint,
            "initial_form": "{ " + ", ".join(f"{k}: {v}" for k, v in initial_form.items()) + " }",
            "form_fields": "\n".join(form_fields),
            "display_fields": "\n".join(display_fields)
        }
        
        for var, value in replacements.items():
            content = content.replace("{{" + var + "}}", str(value))
        
        return content
    
    async def _generate_fastapi_crud_router(self, entity_name: str, fields: List[Dict[str, Any]]) -> str:
        """Generate FastAPI CRUD router."""
        template = self.templates["fastapi_crud_router"]
        
        entity_lower = entity_name.lower()
        entity_plural = entity_name.lower() + "s"
        
        replacements = {
            "model_name": entity_name,
            "schema_name": entity_name,
            "entity_name": entity_name,
            "entity_lower": entity_lower,
            "entity_plural": entity_plural,
            "api_prefix": f"/{entity_plural}",
            "tag_name": entity_plural,
            "model_file": f"{entity_lower}_model",
            "schema_file": f"{entity_lower}_schemas"
        }
        
        content = template.template
        for var, value in replacements.items():
            content = content.replace("{{" + var + "}}", str(value))
        
        return content
    
    async def _generate_sqlalchemy_model(self, entity_name: str, fields: List[Dict[str, Any]]) -> str:
        """Generate SQLAlchemy model."""
        template = self.templates["sqlalchemy_model"]
        
        table_name = entity_name.lower() + "s"
        model_fields = []
        to_dict_fields = []
        
        for field in fields:
            field_name = field["name"]
            field_type = field["type"]
            
            # SQLAlchemy column type
            if field_type == "string":
                column_type = "String(255)"
            elif field_type == "text":
                column_type = "Text"
            elif field_type == "number":
                column_type = "Integer"
            elif field_type == "boolean":
                column_type = "Boolean"
            else:
                column_type = "String(255)"
            
            nullable = field.get("required", True) == False
            model_fields.append(f"    {field_name} = Column({column_type}, nullable={nullable})")
            to_dict_fields.append(f'            "{field_name}": self.{field_name},')
        
        replacements = {
            "model_name": entity_name,
            "model_description": f"{entity_name} model for database operations",
            "table_name": table_name,
            "model_fields": "\n".join(model_fields),
            "relationships": "# Add relationships here",
            "repr_field": fields[0]["name"] if fields else "id",
            "to_dict_fields": "\n".join(to_dict_fields)
        }
        
        content = template.template
        for var, value in replacements.items():
            content = content.replace("{{" + var + "}}", str(value))
        
        return content
    
    async def _generate_pydantic_schemas(self, entity_name: str, fields: List[Dict[str, Any]]) -> str:
        """Generate Pydantic schemas."""
        template = self.templates["pydantic_schema"]
        
        base_fields = []
        update_fields = []
        
        for field in fields:
            field_name = field["name"]
            field_type = field["type"]
            
            # Pydantic field type
            if field_type == "string":
                pydantic_type = "str"
            elif field_type == "text":
                pydantic_type = "str"
            elif field_type == "number":
                pydantic_type = "int"
            elif field_type == "boolean":
                pydantic_type = "bool"
            else:
                pydantic_type = "str"
            
            required = field.get("required", True)
            if required:
                base_fields.append(f"    {field_name}: {pydantic_type}")
                update_fields.append(f"    {field_name}: Optional[{pydantic_type}] = None")
            else:
                base_fields.append(f"    {field_name}: Optional[{pydantic_type}] = None")
                update_fields.append(f"    {field_name}: Optional[{pydantic_type}] = None")
        
        replacements = {
            "schema_name": entity_name,
            "entity_name": entity_name,
            "base_fields": "\n".join(base_fields),
            "create_validators": "    # Add validators here",
            "update_fields": "\n".join(update_fields)
        }
        
        content = template.template
        for var, value in replacements.items():
            content = content.replace("{{" + var + "}}", str(value))
        
        return content
    
    async def _generate_component_css(self, entity_name: str) -> str:
        """Generate CSS for React component."""
        component_class = f"{entity_name.lower()}manager"
        
        return f""".{component_class} {{
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}}

.{component_class} h2 {{
  color: #333;
  margin-bottom: 20px;
}}

.{entity_name.lower()}-form {{
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 30px;
}}

.{entity_name.lower()}-form label {{
  display: block;
  margin-bottom: 15px;
  font-weight: 500;
}}

.{entity_name.lower()}-form input {{
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-top: 5px;
}}

.{entity_name.lower()}-form button {{
  background: #007bff;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  margin-right: 10px;
}}

.{entity_name.lower()}-form button:hover {{
  background: #0056b3;
}}

.{entity_name.lower()}-list {{
  display: grid;
  gap: 15px;
}}

.{entity_name.lower()}-item {{
  background: white;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}}

.{entity_name.lower()}-item .actions {{
  margin-top: 15px;
  display: flex;
  gap: 10px;
}}

.{entity_name.lower()}-item .actions button {{
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}}

.{entity_name.lower()}-item .actions button:first-child {{
  background: #28a745;
  color: white;
}}

.{entity_name.lower()}-item .actions button:last-child {{
  background: #dc3545;
  color: white;
}}

.loading, .error {{
  text-align: center;
  padding: 20px;
  font-size: 18px;
}}

.error {{
  color: #dc3545;
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 4px;
}}"""
