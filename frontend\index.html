<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Augmented Agent Chat</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .chat-log {
            background: #f9f9f9;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            min-height: 300px;
            max-height: 500px;
            overflow-y: auto;
            margin-bottom: 20px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .message {
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 4px;
        }
        .user-message {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
        }
        .assistant-message {
            background: #f3e5f5;
            border-left: 4px solid #9c27b0;
        }
        .input-area {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
        }
        textarea {
            flex: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            resize: vertical;
            font-family: Arial, sans-serif;
        }
        button {
            padding: 10px 20px;
            background: #2196f3;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #1976d2;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .status {
            text-align: center;
            margin-bottom: 10px;
            font-weight: bold;
        }
        .status.connected {
            color: green;
        }
        .status.disconnected {
            color: red;
        }
        .examples {
            margin-top: 20px;
            padding: 15px;
            background: #fff3cd;
            border-radius: 4px;
            border-left: 4px solid #ffc107;
        }
        .examples h3 {
            margin-top: 0;
        }
        .example-button {
            background: #ffc107;
            color: #333;
            margin: 5px;
            padding: 5px 10px;
            font-size: 14px;
        }
        .example-button:hover {
            background: #ffb300;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 Augmented Agent Chat</h1>
        
        <div id="status" class="status disconnected">
            Checking connection to backend...
        </div>
        
        <div id="chatLog" class="chat-log">
            <div class="message assistant-message">
                <strong>Assistant:</strong> Hello! I'm your augmented programming assistant. I can help you with:
                <br>• Running Python code
                <br>• Reading and writing files in your project
                <br>• Solving programming problems
                <br><br>Try asking me something or use one of the examples below!
            </div>
        </div>
        
        <div class="input-area">
            <textarea id="userInput" rows="3" placeholder="Ask me anything! For example: 'Calculate the factorial of 5 using Python' or 'Create a simple Python function to reverse a string'"></textarea>
            <button id="sendButton" onclick="sendMessage()">Send</button>
        </div>
        
        <div class="examples">
            <h3>💡 Try these examples:</h3>
            <button class="example-button" onclick="setInput('Calculate the factorial of 5 using Python')">Calculate factorial</button>
            <button class="example-button" onclick="setInput('Create a simple Python function to reverse a string')">Reverse string function</button>
            <button class="example-button" onclick="setInput('Write a Python script that generates the first 10 Fibonacci numbers')">Fibonacci sequence</button>
            <button class="example-button" onclick="setInput('Create a file called hello.txt with the content Hello World')">Create file</button>
            <button class="example-button" onclick="setInput('Read the contents of hello.txt')">Read file</button>
        </div>
    </div>

    <script>
        let chatHistory = [];
        const API_BASE = 'http://localhost:8000';
        
        // Check backend connection
        async function checkConnection() {
            try {
                const response = await fetch(`${API_BASE}/docs`);
                if (response.ok) {
                    document.getElementById('status').textContent = '✅ Connected to backend server';
                    document.getElementById('status').className = 'status connected';
                } else {
                    throw new Error('Backend not responding');
                }
            } catch (error) {
                document.getElementById('status').textContent = '❌ Backend server not running. Please start the backend first.';
                document.getElementById('status').className = 'status disconnected';
            }
        }
        
        function setInput(text) {
            document.getElementById('userInput').value = text;
        }
        
        function addMessage(role, content) {
            const chatLog = document.getElementById('chatLog');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${role}-message`;
            messageDiv.innerHTML = `<strong>${role.charAt(0).toUpperCase() + role.slice(1)}:</strong> ${content.replace(/\n/g, '<br>')}`;
            chatLog.appendChild(messageDiv);
            chatLog.scrollTop = chatLog.scrollHeight;
        }
        
        async function sendMessage() {
            const input = document.getElementById('userInput');
            const sendButton = document.getElementById('sendButton');
            const message = input.value.trim();
            
            if (!message) return;
            
            // Disable input while processing
            sendButton.disabled = true;
            sendButton.textContent = 'Sending...';
            
            // Add user message to chat
            addMessage('user', message);
            chatHistory.push({role: 'user', content: message});
            
            try {
                const response = await fetch(`${API_BASE}/chat`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        messages: chatHistory.slice(0, -1), // Don't include the current message
                        input: message
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                
                // Add assistant response to chat
                addMessage('assistant', data.reply);
                chatHistory.push({role: 'assistant', content: data.reply});
                
            } catch (error) {
                console.error('Error:', error);
                addMessage('assistant', `❌ Error: ${error.message}. Make sure the backend server is running and you have set your OpenAI API key.`);
            }
            
            // Re-enable input
            input.value = '';
            sendButton.disabled = false;
            sendButton.textContent = 'Send';
            input.focus();
        }
        
        // Allow Enter to send message (Shift+Enter for new line)
        document.getElementById('userInput').addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });
        
        // Check connection on page load
        checkConnection();
        
        // Recheck connection every 10 seconds
        setInterval(checkConnection, 10000);
    </script>
</body>
</html>
