# backend/advanced_agent.py
"""
Advanced Agent System that mimics sophisticated AI assistant capabilities.
This system includes reasoning chains, tool selection, context management, and intelligent response generation.
"""

import json
import re
import os
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime
from pathlib import Path
import asyncio
from openai import OpenAI

@dataclass
class ToolCall:
    """Represents a tool call with metadata."""
    tool_name: str
    parameters: Dict[str, Any]
    reasoning: str
    confidence: float
    timestamp: datetime

@dataclass
class ConversationContext:
    """Manages conversation context and memory."""
    messages: List[Dict[str, str]]
    project_context: Dict[str, Any]
    active_files: List[str]
    recent_operations: List[str]
    user_preferences: Dict[str, Any]
    session_id: str

class AdvancedReasoningEngine:
    """Handles sophisticated reasoning patterns similar to advanced AI assistants."""
    
    def __init__(self):
        self.reasoning_patterns = {
            "code_analysis": self._analyze_code_request,
            "file_operations": self._analyze_file_request,
            "problem_solving": self._analyze_problem_request,
            "information_gathering": self._analyze_info_request,
            "task_planning": self._analyze_planning_request
        }
    
    def analyze_request(self, user_input: str, context: ConversationContext) -> Dict[str, Any]:
        """Analyze user request and determine appropriate response strategy."""
        analysis = {
            "intent": self._classify_intent(user_input),
            "complexity": self._assess_complexity(user_input),
            "required_tools": self._identify_required_tools(user_input, context),
            "reasoning_chain": self._build_reasoning_chain(user_input, context),
            "response_strategy": self._determine_response_strategy(user_input, context)
        }
        return analysis
    
    def _classify_intent(self, user_input: str) -> str:
        """Classify the user's intent."""
        input_lower = user_input.lower()
        
        if any(keyword in input_lower for keyword in ["create", "build", "make", "generate", "write"]):
            return "creation"
        elif any(keyword in input_lower for keyword in ["fix", "debug", "error", "issue", "problem"]):
            return "debugging"
        elif any(keyword in input_lower for keyword in ["explain", "how", "what", "why", "understand"]):
            return "explanation"
        elif any(keyword in input_lower for keyword in ["analyze", "review", "check", "examine"]):
            return "analysis"
        elif any(keyword in input_lower for keyword in ["test", "run", "execute", "try"]):
            return "execution"
        else:
            return "general"
    
    def _assess_complexity(self, user_input: str) -> str:
        """Assess the complexity of the request."""
        complexity_indicators = {
            "simple": ["hello", "hi", "thanks", "yes", "no"],
            "medium": ["create", "write", "fix", "explain"],
            "complex": ["build", "design", "architecture", "system", "integrate", "optimize"]
        }
        
        input_lower = user_input.lower()
        word_count = len(user_input.split())
        
        if word_count < 5 or any(word in input_lower for word in complexity_indicators["simple"]):
            return "simple"
        elif word_count > 20 or any(word in input_lower for word in complexity_indicators["complex"]):
            return "complex"
        else:
            return "medium"
    
    def _identify_required_tools(self, user_input: str, context: ConversationContext) -> List[str]:
        """Identify which tools are likely needed for the request."""
        tools = []
        input_lower = user_input.lower()
        
        # Code execution indicators
        if any(keyword in input_lower for keyword in ["run", "execute", "calculate", "python", "code"]):
            tools.append("run_python")
        
        # File operation indicators
        if any(keyword in input_lower for keyword in ["file", "read", "write", "create", "save"]):
            tools.extend(["read_file", "write_file"])
        
        # Analysis indicators
        if any(keyword in input_lower for keyword in ["analyze", "review", "codebase", "project"]):
            tools.append("codebase_analysis")
        
        # Web search indicators
        if any(keyword in input_lower for keyword in ["search", "find", "lookup", "documentation"]):
            tools.append("web_search")
        
        return tools
    
    def _build_reasoning_chain(self, user_input: str, context: ConversationContext) -> List[str]:
        """Build a reasoning chain for the request."""
        chain = []
        intent = self._classify_intent(user_input)
        
        if intent == "creation":
            chain = [
                "Understand requirements",
                "Analyze existing context",
                "Design solution approach",
                "Implement step by step",
                "Test and validate",
                "Provide explanation"
            ]
        elif intent == "debugging":
            chain = [
                "Identify the problem",
                "Gather relevant information",
                "Analyze potential causes",
                "Propose solutions",
                "Test solutions",
                "Explain the fix"
            ]
        elif intent == "explanation":
            chain = [
                "Understand the topic",
                "Gather relevant information",
                "Structure explanation",
                "Provide examples",
                "Clarify complex points"
            ]
        else:
            chain = [
                "Analyze request",
                "Determine approach",
                "Execute solution",
                "Provide response"
            ]
        
        return chain
    
    def _determine_response_strategy(self, user_input: str, context: ConversationContext) -> Dict[str, Any]:
        """Determine the best response strategy."""
        return {
            "approach": "step_by_step" if self._assess_complexity(user_input) == "complex" else "direct",
            "include_reasoning": True,
            "show_code": "code" in user_input.lower() or "python" in user_input.lower(),
            "provide_examples": "example" in user_input.lower() or "show" in user_input.lower(),
            "be_detailed": self._assess_complexity(user_input) in ["medium", "complex"]
        }

class AdvancedAgent:
    """Advanced agent that mimics sophisticated AI assistant behavior."""
    
    def __init__(self, api_key: Optional[str] = None):
        self.client = OpenAI(api_key=api_key) if api_key else None
        self.reasoning_engine = AdvancedReasoningEngine()
        self.context = ConversationContext(
            messages=[],
            project_context={},
            active_files=[],
            recent_operations=[],
            user_preferences={},
            session_id=datetime.now().isoformat()
        )
        self.tools = self._initialize_tools()
    
    def _initialize_tools(self) -> Dict[str, Any]:
        """Initialize available tools."""
        from .advanced_tools import (
            run_python_advanced, 
            read_file_advanced, 
            write_file_advanced,
            analyze_codebase,
            web_search_simulation,
            run_tests,
            manage_processes
        )
        
        return {
            "run_python": run_python_advanced,
            "read_file": read_file_advanced,
            "write_file": write_file_advanced,
            "analyze_codebase": analyze_codebase,
            "web_search": web_search_simulation,
            "run_tests": run_tests,
            "manage_processes": manage_processes
        }
    
    async def process_request(self, user_input: str) -> str:
        """Process user request with advanced reasoning and tool usage."""
        # Add user message to context
        self.context.messages.append({"role": "user", "content": user_input})
        
        # Analyze the request
        analysis = self.reasoning_engine.analyze_request(user_input, self.context)
        
        # Generate response based on analysis
        response = await self._generate_response(user_input, analysis)
        
        # Add assistant response to context
        self.context.messages.append({"role": "assistant", "content": response})
        
        return response
    
    async def _generate_response(self, user_input: str, analysis: Dict[str, Any]) -> str:
        """Generate sophisticated response based on analysis."""
        strategy = analysis["response_strategy"]
        
        if strategy["approach"] == "step_by_step":
            return await self._generate_step_by_step_response(user_input, analysis)
        else:
            return await self._generate_direct_response(user_input, analysis)
    
    async def _generate_step_by_step_response(self, user_input: str, analysis: Dict[str, Any]) -> str:
        """Generate a detailed step-by-step response."""
        response_parts = []
        
        # Add reasoning if requested
        if analysis["response_strategy"]["include_reasoning"]:
            response_parts.append(f"I'll help you with this request. Here's my approach:")
            for i, step in enumerate(analysis["reasoning_chain"], 1):
                response_parts.append(f"{i}. {step}")
            response_parts.append("")
        
        # Execute required tools
        tool_results = await self._execute_tools(analysis["required_tools"], user_input)
        
        # Generate main response
        main_response = await self._generate_main_response(user_input, analysis, tool_results)
        response_parts.append(main_response)
        
        return "\n".join(response_parts)
    
    async def _generate_direct_response(self, user_input: str, analysis: Dict[str, Any]) -> str:
        """Generate a direct response."""
        # Execute required tools
        tool_results = await self._execute_tools(analysis["required_tools"], user_input)
        
        # Generate main response
        return await self._generate_main_response(user_input, analysis, tool_results)
    
    async def _execute_tools(self, required_tools: List[str], user_input: str) -> Dict[str, Any]:
        """Execute required tools and return results."""
        results = {}
        
        for tool_name in required_tools:
            if tool_name in self.tools:
                try:
                    # Extract parameters for the tool based on user input
                    params = self._extract_tool_parameters(tool_name, user_input)
                    result = await self._call_tool(tool_name, params)
                    results[tool_name] = result
                except Exception as e:
                    results[tool_name] = f"Error: {str(e)}"
        
        return results
    
    def _extract_tool_parameters(self, tool_name: str, user_input: str) -> Dict[str, Any]:
        """Extract parameters for a specific tool from user input."""
        # This would be more sophisticated in a real implementation
        if tool_name == "run_python":
            # Extract code from user input or generate based on request
            return {"code": self._extract_or_generate_code(user_input)}
        elif tool_name in ["read_file", "write_file"]:
            # Extract file path from user input
            return {"path": self._extract_file_path(user_input)}
        else:
            return {"query": user_input}
    
    def _extract_or_generate_code(self, user_input: str) -> str:
        """Extract code from user input or generate appropriate code."""
        # Look for code blocks first
        code_match = re.search(r'```(?:python)?\n(.*?)\n```', user_input, re.DOTALL)
        if code_match:
            return code_match.group(1)
        
        # Generate code based on request
        input_lower = user_input.lower()
        if "factorial" in input_lower:
            return """
def factorial(n):
    if n <= 1:
        return 1
    return n * factorial(n - 1)

result = factorial(5)
print(f"Factorial of 5 is: {result}")
"""
        elif "fibonacci" in input_lower:
            return """
def fibonacci(n):
    fib_sequence = [0, 1]
    for i in range(2, n):
        fib_sequence.append(fib_sequence[i-1] + fib_sequence[i-2])
    return fib_sequence

fib_numbers = fibonacci(10)
print("First 10 Fibonacci numbers:")
for i, num in enumerate(fib_numbers):
    print(f"F({i}) = {num}")
"""
        else:
            return f"# Code generated for: {user_input}\nprint('Hello from generated code!')"
    
    def _extract_file_path(self, user_input: str) -> str:
        """Extract file path from user input."""
        # Simple pattern matching for file paths
        path_match = re.search(r'([a-zA-Z0-9_/\\.-]+\.[a-zA-Z0-9]+)', user_input)
        if path_match:
            return path_match.group(1)
        return "example.txt"
    
    async def _call_tool(self, tool_name: str, params: Dict[str, Any]) -> Any:
        """Call a tool with given parameters."""
        tool_func = self.tools[tool_name]
        if asyncio.iscoroutinefunction(tool_func):
            return await tool_func(**params)
        else:
            return tool_func(**params)
    
    async def _generate_main_response(self, user_input: str, analysis: Dict[str, Any], tool_results: Dict[str, Any]) -> str:
        """Generate the main response content."""
        if self.client:
            return await self._generate_llm_response(user_input, analysis, tool_results)
        else:
            return self._generate_template_response(user_input, analysis, tool_results)
    
    async def _generate_llm_response(self, user_input: str, analysis: Dict[str, Any], tool_results: Dict[str, Any]) -> str:
        """Generate response using LLM."""
        system_prompt = """You are an advanced AI programming assistant. You have access to various tools and should provide helpful, detailed responses. 
        When you use tools, explain what you're doing and why. Format code nicely and provide clear explanations."""
        
        messages = [
            {"role": "system", "content": system_prompt},
            *self.context.messages[-10:],  # Last 10 messages for context
        ]
        
        # Add tool results to the context
        if tool_results:
            tool_context = "Tool execution results:\n"
            for tool, result in tool_results.items():
                tool_context += f"- {tool}: {result}\n"
            messages.append({"role": "system", "content": tool_context})
        
        try:
            response = self.client.chat.completions.create(
                model="gpt-4",
                messages=messages,
                temperature=0.7,
                max_tokens=2000
            )
            return response.choices[0].message.content
        except Exception as e:
            return f"I encountered an error generating a response: {str(e)}"
    
    def _generate_template_response(self, user_input: str, analysis: Dict[str, Any], tool_results: Dict[str, Any]) -> str:
        """Generate response using templates (fallback when no LLM available)."""
        response_parts = []
        
        # Add greeting based on intent
        intent = analysis["intent"]
        if intent == "creation":
            response_parts.append("I'll help you create what you need.")
        elif intent == "debugging":
            response_parts.append("Let me help you debug this issue.")
        elif intent == "explanation":
            response_parts.append("I'll explain this for you.")
        else:
            response_parts.append("I'll help you with this request.")
        
        # Add tool results
        if tool_results:
            response_parts.append("\nHere's what I found:")
            for tool_name, result in tool_results.items():
                response_parts.append(f"\n**{tool_name.replace('_', ' ').title()}:**")
                response_parts.append(str(result))
        
        # Add helpful conclusion
        response_parts.append("\nIs there anything specific you'd like me to explain or modify?")
        
        return "\n".join(response_parts)
