# backend/claude_tools.py
"""
<PERSON>'s Exact Tool Integration System
This replicates how I strategically select, sequence, and use tools to solve problems
"""

import asyncio
import json
import re
from typing import Dict, List, Any, Optional, Tuple, Set
from dataclasses import dataclass
from enum import Enum
import subprocess
import tempfile
from pathlib import Path

class ToolCategory(Enum):
    """Categories of tools I use, in order of typical usage."""
    INFORMATION_GATHERING = "information_gathering"  # First: understand context
    ANALYSIS = "analysis"                           # Second: analyze what I found
    PLANNING = "planning"                          # Third: organize approach
    IMPLEMENTATION = "implementation"              # Fourth: execute solution
    VALIDATION = "validation"                     # Fifth: test and verify
    COMMUNICATION = "communication"               # Sixth: explain and document

@dataclass
class ToolUsagePattern:
    """How I typically use a specific tool."""
    tool_name: str
    category: ToolCategory
    typical_sequence: int  # When in my workflow I use this
    prerequisites: List[str]  # What I need before using this tool
    outputs_enable: List[str]  # What tools this enables
    confidence_threshold: float  # How confident I need to be to use this
    reasoning_template: str  # Why I use this tool

class ClaudeToolIntegration:
    """Replicates exactly how I select and use tools strategically."""
    
    def __init__(self):
        # My tool usage patterns (how I actually use each tool)
        self.tool_patterns = {
            "codebase-retrieval": ToolUsagePattern(
                tool_name="codebase-retrieval",
                category=ToolCategory.INFORMATION_GATHERING,
                typical_sequence=1,
                prerequisites=[],
                outputs_enable=["view", "str-replace-editor"],
                confidence_threshold=0.3,
                reasoning_template="I need to understand the existing codebase context before making changes"
            ),
            
            "view": ToolUsagePattern(
                tool_name="view",
                category=ToolCategory.ANALYSIS,
                typical_sequence=2,
                prerequisites=["codebase-retrieval"],
                outputs_enable=["str-replace-editor", "launch-process"],
                confidence_threshold=0.5,
                reasoning_template="I need to examine the specific file to understand its current state"
            ),
            
            "str-replace-editor": ToolUsagePattern(
                tool_name="str-replace-editor",
                category=ToolCategory.IMPLEMENTATION,
                typical_sequence=4,
                prerequisites=["view"],
                outputs_enable=["launch-process", "diagnostics"],
                confidence_threshold=0.7,
                reasoning_template="I'm making targeted changes based on my analysis"
            ),
            
            "save-file": ToolUsagePattern(
                tool_name="save-file",
                category=ToolCategory.IMPLEMENTATION,
                typical_sequence=4,
                prerequisites=[],
                outputs_enable=["view", "launch-process"],
                confidence_threshold=0.6,
                reasoning_template="I'm creating a new file with the solution"
            ),
            
            "launch-process": ToolUsagePattern(
                tool_name="launch-process",
                category=ToolCategory.VALIDATION,
                typical_sequence=5,
                prerequisites=["str-replace-editor", "save-file"],
                outputs_enable=["read-process"],
                confidence_threshold=0.8,
                reasoning_template="I need to test the implementation to ensure it works"
            ),
            
            "web-search": ToolUsagePattern(
                tool_name="web-search",
                category=ToolCategory.INFORMATION_GATHERING,
                typical_sequence=1,
                prerequisites=[],
                outputs_enable=["web-fetch"],
                confidence_threshold=0.4,
                reasoning_template="I need to find current information or documentation"
            ),
            
            "add_tasks": ToolUsagePattern(
                tool_name="add_tasks",
                category=ToolCategory.PLANNING,
                typical_sequence=3,
                prerequisites=[],
                outputs_enable=["update_tasks"],
                confidence_threshold=0.6,
                reasoning_template="This is complex enough to benefit from structured task management"
            ),
            
            "remember": ToolUsagePattern(
                tool_name="remember",
                category=ToolCategory.COMMUNICATION,
                typical_sequence=6,
                prerequisites=[],
                outputs_enable=[],
                confidence_threshold=0.5,
                reasoning_template="This information will be useful for future conversations"
            )
        }
        
        # My tool selection logic (how I decide which tools to use)
        self.selection_criteria = {
            "user_request_analysis": self._analyze_user_request_for_tools,
            "context_requirements": self._determine_context_requirements,
            "implementation_needs": self._assess_implementation_needs,
            "validation_requirements": self._determine_validation_needs,
            "communication_needs": self._assess_communication_needs
        }
        
        # My tool sequencing rules (the order I use tools)
        self.sequencing_rules = {
            "information_before_action": "Always gather information before taking action",
            "analysis_before_implementation": "Always analyze before implementing",
            "implementation_before_validation": "Always implement before validating",
            "validation_before_communication": "Always validate before final communication"
        }
    
    async def plan_tool_usage(self, user_request: str, context: Dict[str, Any], reasoning_result: Dict[str, Any]) -> Dict[str, Any]:
        """Plan tool usage exactly like I do - strategic and purposeful."""
        
        # Step 1: Analyze what tools are needed (my initial assessment)
        needed_tools = await self._identify_needed_tools(user_request, context, reasoning_result)
        
        # Step 2: Sequence tools properly (my strategic ordering)
        tool_sequence = await self._sequence_tools_strategically(needed_tools, context)
        
        # Step 3: Plan tool parameters (how I prepare tool calls)
        tool_plan = await self._plan_tool_parameters(tool_sequence, user_request, context)
        
        # Step 4: Validate tool plan (my sanity check)
        validation_result = await self._validate_tool_plan(tool_plan, reasoning_result)
        
        return {
            "tool_plan": tool_plan,
            "reasoning": validation_result["reasoning"],
            "confidence": validation_result["confidence"],
            "expected_workflow": validation_result["workflow_description"]
        }
    
    async def _identify_needed_tools(self, user_request: str, context: Dict[str, Any], reasoning_result: Dict[str, Any]) -> List[str]:
        """Identify which tools I need, exactly like my decision process."""
        
        needed_tools = set()
        
        # Information gathering needs (what I check first)
        if self._needs_codebase_context(user_request, context):
            needed_tools.add("codebase-retrieval")
        
        if self._needs_web_information(user_request):
            needed_tools.add("web-search")
        
        # Analysis needs (what I examine)
        if self._needs_file_examination(user_request, context):
            needed_tools.add("view")
        
        # Implementation needs (what I build/modify)
        if self._needs_code_modification(user_request, reasoning_result):
            needed_tools.add("str-replace-editor")
        
        if self._needs_new_file_creation(user_request, reasoning_result):
            needed_tools.add("save-file")
        
        # Validation needs (what I test)
        if self._needs_execution_testing(user_request, reasoning_result):
            needed_tools.add("launch-process")
        
        # Planning needs (what I organize)
        if self._needs_task_management(user_request, reasoning_result):
            needed_tools.add("add_tasks")
        
        # Memory needs (what I remember)
        if self._needs_memory_storage(user_request, context):
            needed_tools.add("remember")
        
        return list(needed_tools)
    
    def _needs_codebase_context(self, user_request: str, context: Dict[str, Any]) -> bool:
        """Determine if I need codebase context (my decision logic)."""
        
        # I use codebase-retrieval when:
        context_indicators = [
            "existing" in user_request.lower(),
            "current" in user_request.lower(),
            "project" in user_request.lower(),
            "codebase" in user_request.lower(),
            any(word in user_request.lower() for word in ["modify", "update", "change", "improve"]),
            bool(context.get("project_files")),  # If I know there are project files
            "analyze" in user_request.lower() and "code" in user_request.lower()
        ]
        
        return any(context_indicators)
    
    def _needs_web_information(self, user_request: str) -> bool:
        """Determine if I need web information (my decision logic)."""
        
        # I use web-search when:
        web_indicators = [
            "documentation" in user_request.lower(),
            "latest" in user_request.lower(),
            "current" in user_request.lower() and "version" in user_request.lower(),
            "how to" in user_request.lower() and "install" in user_request.lower(),
            "best practices" in user_request.lower(),
            "examples" in user_request.lower() and "online" in user_request.lower()
        ]
        
        return any(web_indicators)
    
    def _needs_file_examination(self, user_request: str, context: Dict[str, Any]) -> bool:
        """Determine if I need to examine files (my decision logic)."""
        
        # I use view when:
        examination_indicators = [
            any(word in user_request.lower() for word in ["file", "code", "script"]),
            "look at" in user_request.lower(),
            "check" in user_request.lower(),
            "examine" in user_request.lower(),
            "review" in user_request.lower(),
            bool(re.search(r'\w+\.\w+', user_request)),  # Filename pattern
            context.get("mentioned_files")  # If files were mentioned in context
        ]
        
        return any(examination_indicators)
    
    def _needs_code_modification(self, user_request: str, reasoning_result: Dict[str, Any]) -> bool:
        """Determine if I need to modify code (my decision logic)."""
        
        # I use str-replace-editor when:
        modification_indicators = [
            any(word in user_request.lower() for word in ["fix", "change", "update", "modify", "edit"]),
            "add" in user_request.lower() and "function" in user_request.lower(),
            "remove" in user_request.lower(),
            "replace" in user_request.lower(),
            reasoning_result.get("final_approach") == "modification",
            "debug" in user_request.lower()
        ]
        
        return any(modification_indicators)
    
    def _needs_new_file_creation(self, user_request: str, reasoning_result: Dict[str, Any]) -> bool:
        """Determine if I need to create new files (my decision logic)."""
        
        # I use save-file when:
        creation_indicators = [
            any(word in user_request.lower() for word in ["create", "build", "make", "generate"]),
            "new" in user_request.lower() and any(word in user_request.lower() for word in ["file", "script", "component"]),
            reasoning_result.get("final_approach") in ["step_by_step_implementation", "comprehensive_solution"],
            "write" in user_request.lower() and "file" in user_request.lower()
        ]
        
        return any(creation_indicators)
    
    def _needs_execution_testing(self, user_request: str, reasoning_result: Dict[str, Any]) -> bool:
        """Determine if I need to test execution (my decision logic)."""
        
        # I use launch-process when:
        testing_indicators = [
            any(word in user_request.lower() for word in ["test", "run", "execute", "try"]),
            "make sure" in user_request.lower(),
            "verify" in user_request.lower(),
            reasoning_result.get("implementation_plan") and len(reasoning_result["implementation_plan"]) > 3,
            "debug" in user_request.lower(),
            "install" in user_request.lower()
        ]
        
        return any(testing_indicators)
    
    def _needs_task_management(self, user_request: str, reasoning_result: Dict[str, Any]) -> bool:
        """Determine if I need task management (my decision logic)."""
        
        # I use add_tasks when:
        task_indicators = [
            reasoning_result.get("confidence", 1.0) < 0.7,  # Complex/uncertain tasks
            len(user_request.split()) > 50,  # Long, complex requests
            "project" in user_request.lower(),
            "plan" in user_request.lower(),
            "steps" in user_request.lower(),
            reasoning_result.get("final_approach") == "comprehensive_solution",
            len(reasoning_result.get("implementation_plan", [])) > 5
        ]
        
        return any(task_indicators)
    
    def _needs_memory_storage(self, user_request: str, context: Dict[str, Any]) -> bool:
        """Determine if I need to store information (my decision logic)."""
        
        # I use remember when:
        memory_indicators = [
            "remember" in user_request.lower(),
            "note" in user_request.lower(),
            "important" in user_request.lower(),
            "preference" in user_request.lower(),
            "always" in user_request.lower(),
            "future" in user_request.lower(),
            len(context.get("conversation_history", [])) > 5  # Long conversations
        ]
        
        return any(memory_indicators)
    
    async def _sequence_tools_strategically(self, needed_tools: List[str], context: Dict[str, Any]) -> List[str]:
        """Sequence tools in the order I actually use them."""
        
        # Get tool patterns for needed tools
        tool_patterns = [self.tool_patterns[tool] for tool in needed_tools if tool in self.tool_patterns]
        
        # Sort by category and typical sequence (how I naturally order them)
        sorted_tools = sorted(tool_patterns, key=lambda x: (x.category.value, x.typical_sequence))
        
        # Apply my sequencing rules
        final_sequence = []
        
        # Phase 1: Information gathering (I always start here)
        info_tools = [t for t in sorted_tools if t.category == ToolCategory.INFORMATION_GATHERING]
        final_sequence.extend([t.tool_name for t in info_tools])
        
        # Phase 2: Analysis (I examine what I found)
        analysis_tools = [t for t in sorted_tools if t.category == ToolCategory.ANALYSIS]
        final_sequence.extend([t.tool_name for t in analysis_tools])
        
        # Phase 3: Planning (I organize complex work)
        planning_tools = [t for t in sorted_tools if t.category == ToolCategory.PLANNING]
        final_sequence.extend([t.tool_name for t in planning_tools])
        
        # Phase 4: Implementation (I build the solution)
        impl_tools = [t for t in sorted_tools if t.category == ToolCategory.IMPLEMENTATION]
        final_sequence.extend([t.tool_name for t in impl_tools])
        
        # Phase 5: Validation (I test what I built)
        validation_tools = [t for t in sorted_tools if t.category == ToolCategory.VALIDATION]
        final_sequence.extend([t.tool_name for t in validation_tools])
        
        # Phase 6: Communication (I document and remember)
        comm_tools = [t for t in sorted_tools if t.category == ToolCategory.COMMUNICATION]
        final_sequence.extend([t.tool_name for t in comm_tools])
        
        return final_sequence
    
    async def _plan_tool_parameters(self, tool_sequence: List[str], user_request: str, context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Plan parameters for each tool call (how I prepare my tool usage)."""
        
        tool_plan = []
        
        for tool_name in tool_sequence:
            parameters = await self._determine_tool_parameters(tool_name, user_request, context, tool_plan)
            
            tool_plan.append({
                "tool": tool_name,
                "parameters": parameters,
                "reasoning": self.tool_patterns[tool_name].reasoning_template,
                "expected_output": self._predict_tool_output(tool_name, parameters),
                "confidence": self.tool_patterns[tool_name].confidence_threshold
            })
        
        return tool_plan
    
    async def _determine_tool_parameters(self, tool_name: str, user_request: str, context: Dict[str, Any], previous_tools: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Determine parameters for a specific tool (how I prepare each call)."""
        
        if tool_name == "codebase-retrieval":
            # Extract what I'm looking for
            search_terms = self._extract_search_terms(user_request)
            return {"information_request": search_terms}
        
        elif tool_name == "view":
            # Determine what file to examine
            file_path = self._extract_file_path(user_request, context, previous_tools)
            return {"path": file_path, "type": "file"}
        
        elif tool_name == "str-replace-editor":
            # Plan the edit
            return {
                "command": "str_replace",
                "path": self._get_target_file(user_request, context),
                "instruction_reminder": "ALWAYS BREAK DOWN EDITS INTO SMALLER CHUNKS OF AT MOST 150 LINES EACH."
            }
        
        elif tool_name == "save-file":
            # Plan the new file
            return {
                "path": self._determine_new_file_path(user_request),
                "instructions_reminder": "LIMIT THE FILE CONTENT TO AT MOST 300 LINES. IF MORE CONTENT NEEDS TO BE ADDED USE THE str-replace-editor TOOL TO EDIT THE FILE AFTER IT HAS BEEN CREATED."
            }
        
        elif tool_name == "launch-process":
            # Determine what to run
            command = self._determine_command(user_request, context)
            return {
                "command": command,
                "wait": True,
                "max_wait_seconds": 30,
                "cwd": context.get("working_directory", ".")
            }
        
        elif tool_name == "web-search":
            # Determine search query
            query = self._extract_search_query(user_request)
            return {"query": query}
        
        elif tool_name == "add_tasks":
            # Plan task structure
            return {"tasks": [{"name": "Main Task", "description": user_request}]}
        
        elif tool_name == "remember":
            # Determine what to remember
            memory_content = self._extract_memory_content(user_request, context)
            return {"memory": memory_content}
        
        return {}
    
    def _extract_search_terms(self, user_request: str) -> str:
        """Extract search terms like I identify key concepts."""
        # Simple extraction - in real implementation, this would be more sophisticated
        key_terms = []
        
        # Look for technical terms
        tech_words = re.findall(r'\b[A-Z][a-z]+(?:[A-Z][a-z]+)*\b', user_request)  # CamelCase
        key_terms.extend(tech_words)
        
        # Look for quoted terms
        quoted_terms = re.findall(r'"([^"]*)"', user_request)
        key_terms.extend(quoted_terms)
        
        # Look for important keywords
        important_words = [word for word in user_request.split() if len(word) > 6 and word.lower() not in ['function', 'create', 'please']]
        key_terms.extend(important_words[:3])  # Top 3
        
        return " ".join(key_terms) if key_terms else user_request[:50]
    
    def _extract_file_path(self, user_request: str, context: Dict[str, Any], previous_tools: List[Dict[str, Any]]) -> str:
        """Extract file path like I identify target files."""
        # Look for file patterns
        file_match = re.search(r'\b\w+\.\w+\b', user_request)
        if file_match:
            return file_match.group()
        
        # Check previous tool outputs
        for tool in previous_tools:
            if tool["tool"] == "codebase-retrieval" and "file" in str(tool.get("expected_output", "")):
                return "identified_file.py"  # Placeholder
        
        return "main.py"  # Default
    
    def _get_target_file(self, user_request: str, context: Dict[str, Any]) -> str:
        """Get target file for editing."""
        return self._extract_file_path(user_request, context, [])
    
    def _determine_new_file_path(self, user_request: str) -> str:
        """Determine path for new file."""
        if "component" in user_request.lower():
            return "src/components/NewComponent.js"
        elif "api" in user_request.lower():
            return "api/new_endpoint.py"
        else:
            return "new_file.py"
    
    def _determine_command(self, user_request: str, context: Dict[str, Any]) -> str:
        """Determine command to run."""
        if "test" in user_request.lower():
            return "python -m pytest"
        elif "install" in user_request.lower():
            return "pip install -r requirements.txt"
        elif "run" in user_request.lower():
            return "python main.py"
        else:
            return "python --version"
    
    def _extract_search_query(self, user_request: str) -> str:
        """Extract web search query."""
        return user_request[:100]  # Simple truncation
    
    def _extract_memory_content(self, user_request: str, context: Dict[str, Any]) -> str:
        """Extract what to remember."""
        return f"User request: {user_request[:100]}"
    
    def _predict_tool_output(self, tool_name: str, parameters: Dict[str, Any]) -> str:
        """Predict what the tool will return (my expectation setting)."""
        predictions = {
            "codebase-retrieval": "Relevant code context and file information",
            "view": "File contents and structure",
            "str-replace-editor": "Successful edit confirmation",
            "save-file": "New file creation confirmation",
            "launch-process": "Command execution results",
            "web-search": "Relevant documentation and examples",
            "add_tasks": "Task management structure created",
            "remember": "Information stored for future reference"
        }
        
        return predictions.get(tool_name, "Tool execution result")
    
    async def _validate_tool_plan(self, tool_plan: List[Dict[str, Any]], reasoning_result: Dict[str, Any]) -> Dict[str, Any]:
        """Validate the tool plan (my sanity check)."""
        
        # Check if plan makes sense
        has_info_gathering = any(tool["tool"] in ["codebase-retrieval", "web-search"] for tool in tool_plan)
        has_implementation = any(tool["tool"] in ["str-replace-editor", "save-file"] for tool in tool_plan)
        has_validation = any(tool["tool"] == "launch-process" for tool in tool_plan)
        
        confidence = 0.8
        reasoning_parts = []
        
        if has_info_gathering:
            reasoning_parts.append("✓ Information gathering planned")
        else:
            reasoning_parts.append("⚠ No information gathering - proceeding with existing knowledge")
            confidence -= 0.1
        
        if has_implementation:
            reasoning_parts.append("✓ Implementation steps planned")
        else:
            reasoning_parts.append("⚠ No implementation - providing guidance only")
        
        if has_validation and has_implementation:
            reasoning_parts.append("✓ Validation planned after implementation")
        elif has_implementation:
            reasoning_parts.append("⚠ No validation planned - will rely on static analysis")
            confidence -= 0.1
        
        workflow_description = f"Planned {len(tool_plan)} tool calls in strategic sequence: {' → '.join([tool['tool'] for tool in tool_plan])}"
        
        return {
            "confidence": confidence,
            "reasoning": "\n".join(reasoning_parts),
            "workflow_description": workflow_description,
            "validated": True
        }
