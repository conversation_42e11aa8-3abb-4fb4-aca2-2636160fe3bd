# backend/fullstack_agent.py
"""
Comprehensive Full-Stack Development AI Agent
Specialized for complete web development workflows including:
- Frontend development (Re<PERSON>, Vue, Angular, vanilla JS)
- Backend development (Node.js, Python, PHP, Java)
- Database design and management
- API development and integration
- Testing and deployment automation
- Project management and scaffolding
"""

import os
import json
import asyncio
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime
from pathlib import Path
import subprocess
import shutil
from enum import Enum

class ProjectType(Enum):
    REACT_APP = "react"
    VUE_APP = "vue"
    ANGULAR_APP = "angular"
    VANILLA_JS = "vanilla"
    NODE_API = "node_api"
    PYTHON_API = "python_api"
    PHP_API = "php_api"
    FULLSTACK = "fullstack"
    STATIC_SITE = "static"

class DatabaseType(Enum):
    MYSQL = "mysql"
    POSTGRESQL = "postgresql"
    MONGODB = "mongodb"
    SQLITE = "sqlite"
    REDIS = "redis"

@dataclass
class ProjectConfig:
    """Configuration for a development project."""
    name: str
    type: ProjectType
    description: str
    technologies: List[str]
    database: Optional[DatabaseType] = None
    features: List[str] = None
    deployment_target: str = "local"
    git_repo: Optional[str] = None
    created_at: datetime = None

@dataclass
class DevelopmentContext:
    """Context for development operations."""
    current_project: Optional[ProjectConfig] = None
    active_files: List[str] = None
    recent_changes: List[str] = None
    build_status: str = "idle"
    test_status: str = "not_run"
    deployment_status: str = "not_deployed"
    dependencies: Dict[str, str] = None

class FullStackAgent:
    """Comprehensive AI agent for full-stack web development."""
    
    def __init__(self, workspace_path: str = None):
        self.workspace_path = Path(workspace_path or os.getcwd())
        self.context = DevelopmentContext(
            active_files=[],
            recent_changes=[],
            dependencies={}
        )
        self.project_templates = self._load_project_templates()
        self.code_generators = self._initialize_code_generators()
        
    def _load_project_templates(self) -> Dict[str, Dict]:
        """Load project templates for different tech stacks."""
        return {
            "react": {
                "structure": {
                    "src/": {
                        "components/": {},
                        "pages/": {},
                        "hooks/": {},
                        "utils/": {},
                        "styles/": {},
                        "assets/": {}
                    },
                    "public/": {},
                    "tests/": {}
                },
                "dependencies": {
                    "react": "^18.2.0",
                    "react-dom": "^18.2.0",
                    "react-router-dom": "^6.8.0",
                    "axios": "^1.3.0"
                },
                "dev_dependencies": {
                    "@vitejs/plugin-react": "^3.1.0",
                    "vite": "^4.1.0",
                    "eslint": "^8.35.0",
                    "@testing-library/react": "^14.0.0"
                }
            },
            "vue": {
                "structure": {
                    "src/": {
                        "components/": {},
                        "views/": {},
                        "composables/": {},
                        "utils/": {},
                        "assets/": {}
                    },
                    "public/": {},
                    "tests/": {}
                },
                "dependencies": {
                    "vue": "^3.2.0",
                    "vue-router": "^4.1.0",
                    "pinia": "^2.0.0",
                    "axios": "^1.3.0"
                }
            },
            "node_api": {
                "structure": {
                    "src/": {
                        "controllers/": {},
                        "models/": {},
                        "routes/": {},
                        "middleware/": {},
                        "utils/": {},
                        "config/": {}
                    },
                    "tests/": {},
                    "docs/": {}
                },
                "dependencies": {
                    "express": "^4.18.0",
                    "cors": "^2.8.5",
                    "helmet": "^6.0.0",
                    "dotenv": "^16.0.0",
                    "joi": "^17.7.0"
                }
            },
            "python_api": {
                "structure": {
                    "app/": {
                        "api/": {},
                        "models/": {},
                        "services/": {},
                        "utils/": {},
                        "config/": {}
                    },
                    "tests/": {},
                    "docs/": {}
                },
                "dependencies": {
                    "fastapi": "^0.95.0",
                    "uvicorn": "^0.21.0",
                    "pydantic": "^1.10.0",
                    "sqlalchemy": "^2.0.0",
                    "alembic": "^1.10.0"
                }
            }
        }
    
    def _initialize_code_generators(self) -> Dict[str, Any]:
        """Initialize code generation modules."""
        return {
            "react_component": self._generate_react_component,
            "vue_component": self._generate_vue_component,
            "api_endpoint": self._generate_api_endpoint,
            "database_model": self._generate_database_model,
            "test_suite": self._generate_test_suite,
            "dockerfile": self._generate_dockerfile,
            "ci_cd": self._generate_ci_cd_config
        }
    
    async def create_project(self, config: ProjectConfig) -> Dict[str, Any]:
        """Create a new development project with full scaffolding."""
        try:
            project_path = self.workspace_path / config.name
            
            # Create project directory
            project_path.mkdir(exist_ok=True)
            
            # Get template for project type
            template = self.project_templates.get(config.type.value, {})
            
            # Create directory structure
            await self._create_directory_structure(project_path, template.get("structure", {}))
            
            # Generate configuration files
            await self._generate_config_files(project_path, config, template)
            
            # Initialize package management
            await self._initialize_package_management(project_path, config, template)
            
            # Generate initial code files
            await self._generate_initial_code(project_path, config)
            
            # Initialize git repository
            if config.git_repo:
                await self._initialize_git(project_path, config.git_repo)
            
            # Update context
            self.context.current_project = config
            self.context.recent_changes.append(f"Created project: {config.name}")
            
            return {
                "success": True,
                "project_path": str(project_path),
                "message": f"Successfully created {config.type.value} project: {config.name}",
                "next_steps": self._get_next_steps(config)
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"Failed to create project: {str(e)}"
            }
    
    async def _create_directory_structure(self, base_path: Path, structure: Dict) -> None:
        """Recursively create directory structure."""
        for name, content in structure.items():
            path = base_path / name
            if name.endswith("/"):
                # It's a directory
                path.mkdir(exist_ok=True)
                if isinstance(content, dict):
                    await self._create_directory_structure(path, content)
            else:
                # It's a file
                path.parent.mkdir(parents=True, exist_ok=True)
                if isinstance(content, str):
                    path.write_text(content)
    
    async def _generate_config_files(self, project_path: Path, config: ProjectConfig, template: Dict) -> None:
        """Generate configuration files for the project."""
        
        # Package.json for Node.js projects
        if config.type in [ProjectType.REACT_APP, ProjectType.VUE_APP, ProjectType.NODE_API]:
            package_json = {
                "name": config.name,
                "version": "1.0.0",
                "description": config.description,
                "main": "index.js",
                "scripts": self._get_npm_scripts(config.type),
                "dependencies": template.get("dependencies", {}),
                "devDependencies": template.get("dev_dependencies", {})
            }
            
            (project_path / "package.json").write_text(
                json.dumps(package_json, indent=2)
            )
        
        # Requirements.txt for Python projects
        elif config.type == ProjectType.PYTHON_API:
            requirements = []
            for dep, version in template.get("dependencies", {}).items():
                requirements.append(f"{dep}{version}")
            
            (project_path / "requirements.txt").write_text("\n".join(requirements))
        
        # Environment configuration
        env_content = self._generate_env_config(config)
        (project_path / ".env.example").write_text(env_content)
        
        # README.md
        readme_content = self._generate_readme(config)
        (project_path / "README.md").write_text(readme_content)
        
        # .gitignore
        gitignore_content = self._generate_gitignore(config.type)
        (project_path / ".gitignore").write_text(gitignore_content)
    
    def _get_npm_scripts(self, project_type: ProjectType) -> Dict[str, str]:
        """Get appropriate npm scripts for project type."""
        base_scripts = {
            "test": "jest",
            "lint": "eslint src/",
            "format": "prettier --write src/"
        }
        
        if project_type == ProjectType.REACT_APP:
            base_scripts.update({
                "dev": "vite",
                "build": "vite build",
                "preview": "vite preview"
            })
        elif project_type == ProjectType.VUE_APP:
            base_scripts.update({
                "dev": "vite",
                "build": "vite build",
                "preview": "vite preview"
            })
        elif project_type == ProjectType.NODE_API:
            base_scripts.update({
                "start": "node src/index.js",
                "dev": "nodemon src/index.js",
                "build": "npm run lint && npm run test"
            })
        
        return base_scripts
    
    def _generate_env_config(self, config: ProjectConfig) -> str:
        """Generate environment configuration template."""
        env_vars = [
            "# Environment Configuration",
            f"# Project: {config.name}",
            "",
            "NODE_ENV=development",
            "PORT=3000",
            "",
            "# Database Configuration"
        ]
        
        if config.database:
            if config.database == DatabaseType.MYSQL:
                env_vars.extend([
                    "DB_HOST=localhost",
                    "DB_PORT=3306",
                    "DB_NAME=your_database",
                    "DB_USER=your_username",
                    "DB_PASSWORD=your_password"
                ])
            elif config.database == DatabaseType.POSTGRESQL:
                env_vars.extend([
                    "DATABASE_URL=postgresql://username:password@localhost:5432/database_name"
                ])
            elif config.database == DatabaseType.MONGODB:
                env_vars.extend([
                    "MONGODB_URI=mongodb://localhost:27017/your_database"
                ])
        
        env_vars.extend([
            "",
            "# API Keys",
            "JWT_SECRET=your_jwt_secret_here",
            "API_KEY=your_api_key_here"
        ])
        
        return "\n".join(env_vars)
    
    def _generate_readme(self, config: ProjectConfig) -> str:
        """Generate comprehensive README.md."""
        return f"""# {config.name}

{config.description}

## 🚀 Tech Stack

- **Type**: {config.type.value.title()}
- **Technologies**: {', '.join(config.technologies)}
{f"- **Database**: {config.database.value.title()}" if config.database else ""}

## 📋 Features

{chr(10).join(f"- {feature}" for feature in (config.features or ["Basic functionality"]))}

## 🛠️ Installation

1. Clone the repository
```bash
git clone {config.git_repo or "your-repo-url"}
cd {config.name}
```

2. Install dependencies
```bash
{"npm install" if config.type.value in ["react", "vue", "node_api"] else "pip install -r requirements.txt"}
```

3. Set up environment variables
```bash
cp .env.example .env
# Edit .env with your configuration
```

4. Start development server
```bash
{"npm run dev" if config.type.value in ["react", "vue"] else "npm start" if config.type.value == "node_api" else "python main.py"}
```

## 📁 Project Structure

```
{config.name}/
├── src/                 # Source code
├── tests/              # Test files
├── docs/               # Documentation
├── .env.example        # Environment template
├── README.md           # This file
└── package.json        # Dependencies
```

## 🧪 Testing

```bash
npm test
```

## 🚀 Deployment

```bash
npm run build
```

## 📝 License

MIT License

## 🤝 Contributing

1. Fork the project
2. Create your feature branch
3. Commit your changes
4. Push to the branch
5. Open a pull request

---

Generated by Full-Stack AI Agent 🤖
"""
    
    def _generate_gitignore(self, project_type: ProjectType) -> str:
        """Generate appropriate .gitignore file."""
        common_ignores = [
            "# Dependencies",
            "node_modules/",
            "",
            "# Environment variables",
            ".env",
            ".env.local",
            ".env.production",
            "",
            "# Logs",
            "*.log",
            "logs/",
            "",
            "# Runtime data",
            "pids/",
            "*.pid",
            "*.seed",
            "",
            "# Coverage directory used by tools like istanbul",
            "coverage/",
            "",
            "# IDE files",
            ".vscode/",
            ".idea/",
            "*.swp",
            "*.swo",
            "",
            "# OS generated files",
            ".DS_Store",
            "Thumbs.db"
        ]
        
        if project_type in [ProjectType.REACT_APP, ProjectType.VUE_APP]:
            common_ignores.extend([
                "",
                "# Build outputs",
                "dist/",
                "build/",
                "",
                "# Vite",
                ".vite/"
            ])
        
        if project_type == ProjectType.PYTHON_API:
            common_ignores.extend([
                "",
                "# Python",
                "__pycache__/",
                "*.py[cod]",
                "*$py.class",
                "*.so",
                ".Python",
                "venv/",
                "env/",
                ".pytest_cache/"
            ])
        
        return "\n".join(common_ignores)
    
    async def _initialize_package_management(self, project_path: Path, config: ProjectConfig, template: Dict) -> None:
        """Initialize package management for the project."""
        try:
            if config.type in [ProjectType.REACT_APP, ProjectType.VUE_APP, ProjectType.NODE_API]:
                # Run npm install
                process = await asyncio.create_subprocess_exec(
                    "npm", "install",
                    cwd=project_path,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                await process.communicate()
                
            elif config.type == ProjectType.PYTHON_API:
                # Create virtual environment and install dependencies
                venv_path = project_path / "venv"
                process = await asyncio.create_subprocess_exec(
                    "python", "-m", "venv", str(venv_path),
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                await process.communicate()
                
        except Exception as e:
            print(f"Warning: Could not initialize package management: {e}")
    
    async def _generate_initial_code(self, project_path: Path, config: ProjectConfig) -> None:
        """Generate initial code files based on project type."""

        if config.type == ProjectType.REACT_APP:
            await self._generate_react_starter_files(project_path, config)
        elif config.type == ProjectType.VUE_APP:
            await self._generate_vue_starter_files(project_path, config)
        elif config.type == ProjectType.NODE_API:
            await self._generate_node_api_starter_files(project_path, config)
        elif config.type == ProjectType.PYTHON_API:
            await self._generate_python_api_starter_files(project_path, config)

    async def _generate_react_starter_files(self, project_path: Path, config: ProjectConfig) -> None:
        """Generate React starter files."""
        # Main App component
        app_content = '''import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import './App.css';
import Home from './pages/Home';
import About from './pages/About';

function App() {
  return (
    <Router>
      <div className="App">
        <header className="App-header">
          <nav>
            <h1>{config.name}</h1>
          </nav>
        </header>
        <main>
          <Routes>
            <Route path="/" element={<Home />} />
            <Route path="/about" element={<About />} />
          </Routes>
        </main>
      </div>
    </Router>
  );
}

export default App;'''

        (project_path / "src" / "App.js").write_text(app_content)

        # Home page
        home_content = '''import React from 'react';

const Home = () => {
  return (
    <div className="home">
      <h1>Welcome to {config.name}</h1>
      <p>{config.description}</p>
    </div>
  );
};

export default Home;'''

        (project_path / "src" / "pages" / "Home.js").write_text(home_content)

    async def _generate_vue_starter_files(self, project_path: Path, config: ProjectConfig) -> None:
        """Generate Vue starter files."""
        # Main App component
        app_content = f'''<template>
  <div id="app">
    <nav>
      <router-link to="/">Home</router-link> |
      <router-link to="/about">About</router-link>
    </nav>
    <router-view/>
  </div>
</template>

<script>
export default {{
  name: 'App'
}}
</script>

<style>
#app {{
  font-family: Avenir, Helvetica, Arial, sans-serif;
  text-align: center;
  color: #2c3e50;
  margin-top: 60px;
}}
</style>'''

        (project_path / "src" / "App.vue").write_text(app_content)

    async def _generate_node_api_starter_files(self, project_path: Path, config: ProjectConfig) -> None:
        """Generate Node.js API starter files."""
        # Main server file
        server_content = f'''const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(helmet());
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({{ extended: true }}));

// Routes
app.get('/', (req, res) => {{
  res.json({{
    message: 'Welcome to {config.name} API',
    version: '1.0.0',
    status: 'running'
  }});
}});

app.get('/health', (req, res) => {{
  res.json({{ status: 'healthy', timestamp: new Date().toISOString() }});
}});

// Error handling middleware
app.use((err, req, res, next) => {{
  console.error(err.stack);
  res.status(500).json({{ error: 'Something went wrong!' }});
}});

// 404 handler
app.use('*', (req, res) => {{
  res.status(404).json({{ error: 'Route not found' }});
}});

app.listen(PORT, () => {{
  console.log(`🚀 {config.name} API running on port ${{PORT}}`);
}});

module.exports = app;'''

        (project_path / "src" / "index.js").write_text(server_content)

    async def _generate_python_api_starter_files(self, project_path: Path, config: ProjectConfig) -> None:
        """Generate Python FastAPI starter files."""
        # Main application file
        main_content = f'''from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn
from datetime import datetime

app = FastAPI(
    title="{config.name}",
    description="{config.description}",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

class HealthResponse(BaseModel):
    status: str
    timestamp: str

@app.get("/")
async def root():
    return {{
        "message": "Welcome to {config.name} API",
        "version": "1.0.0",
        "status": "running"
    }}

@app.get("/health", response_model=HealthResponse)
async def health_check():
    return HealthResponse(
        status="healthy",
        timestamp=datetime.now().isoformat()
    )

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)'''

        (project_path / "main.py").write_text(main_content)
    
    async def _initialize_git(self, project_path: Path, repo_url: str) -> None:
        """Initialize git repository."""
        try:
            # Initialize git
            process = await asyncio.create_subprocess_exec(
                "git", "init",
                cwd=project_path,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            await process.communicate()
            
            # Add remote origin if provided
            if repo_url:
                process = await asyncio.create_subprocess_exec(
                    "git", "remote", "add", "origin", repo_url,
                    cwd=project_path,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                await process.communicate()
                
        except Exception as e:
            print(f"Warning: Could not initialize git: {e}")
    
    def _get_next_steps(self, config: ProjectConfig) -> List[str]:
        """Get recommended next steps for the project."""
        steps = [
            f"Navigate to project directory: cd {config.name}",
            "Review and update .env file with your configuration",
            "Start the development server"
        ]
        
        if config.type in [ProjectType.REACT_APP, ProjectType.VUE_APP]:
            steps.append("Begin building your components in src/components/")
        elif config.type in [ProjectType.NODE_API, ProjectType.PYTHON_API]:
            steps.append("Define your API routes and models")
            
        if config.database:
            steps.append("Set up your database connection and run migrations")
            
        steps.extend([
            "Write tests for your functionality",
            "Set up CI/CD pipeline for deployment"
        ])
        
        return steps
