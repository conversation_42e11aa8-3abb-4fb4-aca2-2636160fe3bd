# backend/response_generator.py
"""
Intelligent response generation system that mimics advanced AI assistant behavior.
Includes reasoning chains, code analysis, contextual understanding, and sophisticated formatting.
"""

import re
import json
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
import asyncio

@dataclass
class ResponseContext:
    """Context for response generation."""
    user_input: str
    conversation_history: List[Dict[str, str]]
    tool_results: Dict[str, Any]
    analysis: Dict[str, Any]
    user_preferences: Dict[str, Any]

class ResponseFormatter:
    """Handles sophisticated response formatting."""
    
    @staticmethod
    def format_code_block(code: str, language: str = "python") -> str:
        """Format code with proper syntax highlighting markers."""
        return f"```{language}\n{code.strip()}\n```"
    
    @staticmethod
    def format_file_snippet(file_path: str, content: str, start_line: int = 1) -> str:
        """Format file content as a clickable snippet."""
        lines = content.split('\n')
        formatted_lines = []
        for i, line in enumerate(lines, start_line):
            formatted_lines.append(f"{i:4d} | {line}")
        
        return f"<augment_code_snippet path=\"{file_path}\" mode=\"EXCERPT\">\n```python\n" + "\n".join(formatted_lines) + "\n```\n</augment_code_snippet>"
    
    @staticmethod
    def format_execution_result(result: Dict[str, Any]) -> str:
        """Format code execution results."""
        if not result.get("success", False):
            return f"❌ **Execution Error:**\n```\n{result.get('error', 'Unknown error')}\n```"
        
        output = result.get("output", "")
        execution_time = result.get("execution_time", 0)
        
        formatted = f"✅ **Execution successful** (took {execution_time:.3f}s)\n"
        if output:
            formatted += f"```\n{output}\n```"
        else:
            formatted += "*(No output)*"
        
        return formatted
    
    @staticmethod
    def format_analysis_summary(analysis: Dict[str, Any]) -> str:
        """Format code analysis summary."""
        if not analysis:
            return ""
        
        summary_parts = []
        
        if "functions" in analysis:
            func_count = len(analysis["functions"])
            if func_count > 0:
                summary_parts.append(f"📋 **Functions:** {func_count}")
                for func in analysis["functions"][:3]:  # Show first 3
                    args_str = ", ".join(func.get("args", []))
                    summary_parts.append(f"  • `{func['name']}({args_str})` (line {func['line']})")
                if func_count > 3:
                    summary_parts.append(f"  • ... and {func_count - 3} more")
        
        if "classes" in analysis:
            class_count = len(analysis["classes"])
            if class_count > 0:
                summary_parts.append(f"🏗️ **Classes:** {class_count}")
                for cls in analysis["classes"][:2]:  # Show first 2
                    method_count = len(cls.get("methods", []))
                    summary_parts.append(f"  • `{cls['name']}` ({method_count} methods)")
        
        if "lines_of_code" in analysis:
            summary_parts.append(f"📏 **Lines of code:** {analysis['lines_of_code']}")
        
        return "\n".join(summary_parts) if summary_parts else ""

class IntelligentResponseGenerator:
    """Generates sophisticated responses that mimic advanced AI assistant behavior."""
    
    def __init__(self):
        self.formatter = ResponseFormatter()
        self.response_templates = self._load_response_templates()
    
    def _load_response_templates(self) -> Dict[str, str]:
        """Load response templates for different scenarios."""
        return {
            "code_creation": """I'll help you create that code. Let me break this down:

{reasoning}

{code_block}

{execution_result}

{analysis_summary}

{explanation}""",
            
            "code_analysis": """I'll analyze this code for you:

{analysis_summary}

{detailed_analysis}

{suggestions}""",
            
            "debugging": """I'll help you debug this issue. Here's my analysis:

{problem_identification}

{solution_approach}

{code_fix}

{explanation}""",
            
            "explanation": """Let me explain this concept:

{main_explanation}

{code_examples}

{additional_context}""",
            
            "file_operation": """I'll handle that file operation:

{operation_summary}

{result_details}

{next_steps}"""
        }
    
    async def generate_response(self, context: ResponseContext) -> str:
        """Generate an intelligent response based on context."""
        
        # Determine response type
        response_type = self._determine_response_type(context)
        
        # Generate response based on type
        if response_type == "code_creation":
            return await self._generate_code_creation_response(context)
        elif response_type == "code_analysis":
            return await self._generate_code_analysis_response(context)
        elif response_type == "debugging":
            return await self._generate_debugging_response(context)
        elif response_type == "explanation":
            return await self._generate_explanation_response(context)
        elif response_type == "file_operation":
            return await self._generate_file_operation_response(context)
        else:
            return await self._generate_general_response(context)
    
    def _determine_response_type(self, context: ResponseContext) -> str:
        """Determine the type of response needed."""
        user_input = context.user_input.lower()
        tool_results = context.tool_results
        
        if "run_python" in tool_results and any(keyword in user_input for keyword in ["create", "write", "build", "make"]):
            return "code_creation"
        elif "analyze_codebase" in tool_results or "analysis" in user_input:
            return "code_analysis"
        elif any(keyword in user_input for keyword in ["fix", "debug", "error", "issue", "problem"]):
            return "debugging"
        elif any(keyword in user_input for keyword in ["explain", "how", "what", "why"]):
            return "explanation"
        elif any(keyword in tool_results for keyword in ["read_file", "write_file"]):
            return "file_operation"
        else:
            return "general"
    
    async def _generate_code_creation_response(self, context: ResponseContext) -> str:
        """Generate response for code creation requests."""
        response_parts = []
        
        # Add reasoning
        reasoning = self._generate_reasoning_chain(context)
        if reasoning:
            response_parts.append(f"**My approach:**\n{reasoning}")
        
        # Add code execution results
        if "run_python" in context.tool_results:
            result = context.tool_results["run_python"]
            
            # Show the code
            if "analysis" in result and "code" in str(result):
                # Extract code from the result context
                code = self._extract_code_from_context(context)
                if code:
                    response_parts.append(f"**Here's the code I created:**\n{self.formatter.format_code_block(code)}")
            
            # Show execution result
            response_parts.append(self.formatter.format_execution_result(result))
            
            # Show analysis if available
            if "analysis" in result:
                analysis_summary = self.formatter.format_analysis_summary(result["analysis"])
                if analysis_summary:
                    response_parts.append(f"**Code Analysis:**\n{analysis_summary}")
        
        # Add explanation
        explanation = self._generate_code_explanation(context)
        if explanation:
            response_parts.append(f"**Explanation:**\n{explanation}")
        
        # Add suggestions for next steps
        suggestions = self._generate_next_steps(context)
        if suggestions:
            response_parts.append(f"**Next steps:**\n{suggestions}")
        
        return "\n\n".join(response_parts)
    
    async def _generate_code_analysis_response(self, context: ResponseContext) -> str:
        """Generate response for code analysis requests."""
        response_parts = []
        
        if "analyze_codebase" in context.tool_results:
            result = context.tool_results["analyze_codebase"]
            
            if result.get("success"):
                analysis = result["analysis"]
                
                # Overview
                response_parts.append("📊 **Codebase Analysis Results:**")
                response_parts.append(f"• **Total files:** {analysis['total_files']}")
                response_parts.append(f"• **Total lines:** {analysis['total_lines']:,}")
                
                # File types breakdown
                if analysis["file_types"]:
                    response_parts.append("\n**File Types:**")
                    for ext, data in sorted(analysis["file_types"].items(), key=lambda x: x[1]["count"], reverse=True):
                        response_parts.append(f"• `{ext}`: {data['count']} files ({data['lines']:,} lines)")
                
                # Python-specific analysis
                if analysis["python_analysis"]["total_functions"] > 0:
                    py_analysis = analysis["python_analysis"]
                    response_parts.append(f"\n🐍 **Python Code:**")
                    response_parts.append(f"• **Functions:** {py_analysis['total_functions']}")
                    response_parts.append(f"• **Classes:** {py_analysis['total_classes']}")
                
                # Largest files
                if analysis["largest_files"]:
                    response_parts.append("\n📄 **Largest Files:**")
                    for file_info in analysis["largest_files"][:5]:
                        response_parts.append(f"• `{file_info['path']}`: {file_info['lines']} lines")
                
                # Issues
                if analysis["issues"]:
                    response_parts.append(f"\n⚠️ **Issues Found:** {len(analysis['issues'])}")
                    for issue in analysis["issues"][:3]:
                        response_parts.append(f"• `{issue['file']}`: {issue['message']}")
                    if len(analysis["issues"]) > 3:
                        response_parts.append(f"• ... and {len(analysis['issues']) - 3} more issues")
            else:
                response_parts.append(f"❌ **Analysis failed:** {result.get('error', 'Unknown error')}")
        
        return "\n".join(response_parts)
    
    async def _generate_debugging_response(self, context: ResponseContext) -> str:
        """Generate response for debugging requests."""
        response_parts = []
        
        # Problem identification
        response_parts.append("🔍 **Problem Analysis:**")
        problem_analysis = self._analyze_problem(context)
        response_parts.append(problem_analysis)
        
        # Solution approach
        if "run_python" in context.tool_results:
            result = context.tool_results["run_python"]
            
            if not result.get("success"):
                response_parts.append("\n🛠️ **Debugging Steps:**")
                error = result.get("error", "")
                
                if "SyntaxError" in error:
                    response_parts.append("1. **Syntax Error detected** - Let me fix the syntax issues")
                    response_parts.append("2. **Check indentation and brackets**")
                elif "NameError" in error:
                    response_parts.append("1. **Variable/function not defined** - Let me check the scope")
                    response_parts.append("2. **Verify imports and definitions**")
                elif "TypeError" in error:
                    response_parts.append("1. **Type mismatch detected** - Let me check data types")
                    response_parts.append("2. **Verify function arguments**")
                else:
                    response_parts.append("1. **Analyzing the error pattern**")
                    response_parts.append("2. **Proposing solution**")
                
                response_parts.append(f"\n**Error Details:**\n```\n{error}\n```")
            else:
                response_parts.append("\n✅ **Code executed successfully!**")
                response_parts.append(self.formatter.format_execution_result(result))
        
        return "\n".join(response_parts)
    
    async def _generate_explanation_response(self, context: ResponseContext) -> str:
        """Generate response for explanation requests."""
        response_parts = []
        
        # Main explanation
        explanation = self._generate_detailed_explanation(context)
        response_parts.append(explanation)
        
        # Code examples if relevant
        if "run_python" in context.tool_results:
            result = context.tool_results["run_python"]
            code = self._extract_code_from_context(context)
            
            if code:
                response_parts.append(f"\n**Example Code:**\n{self.formatter.format_code_block(code)}")
                
                if result.get("success"):
                    response_parts.append(f"\n**Output:**\n```\n{result.get('output', '')}\n```")
        
        # Additional context
        additional_context = self._generate_additional_context(context)
        if additional_context:
            response_parts.append(f"\n**Additional Information:**\n{additional_context}")
        
        return "\n".join(response_parts)
    
    async def _generate_file_operation_response(self, context: ResponseContext) -> str:
        """Generate response for file operations."""
        response_parts = []
        
        # File read operations
        if "read_file" in context.tool_results:
            result = context.tool_results["read_file"]
            
            if result.get("success"):
                response_parts.append(f"📖 **Successfully read file:** `{result['path']}`")
                
                analysis = result.get("analysis", {})
                if analysis:
                    response_parts.append(f"• **Size:** {analysis.get('size', 0)} bytes")
                    response_parts.append(f"• **Lines:** {analysis.get('lines', 0)}")
                    response_parts.append(f"• **Modified:** {analysis.get('modified', 'Unknown')}")
                
                # Show code snippet for code files
                content = result.get("content", "")
                if content and len(content.splitlines()) <= 50:  # Show full content for small files
                    response_parts.append(f"\n**File Content:**\n{self.formatter.format_code_block(content)}")
                elif content:
                    # Show first few lines for large files
                    lines = content.splitlines()
                    preview = "\n".join(lines[:10])
                    response_parts.append(f"\n**File Preview (first 10 lines):**\n{self.formatter.format_code_block(preview)}")
                    response_parts.append(f"*... and {len(lines) - 10} more lines*")
            else:
                response_parts.append(f"❌ **Failed to read file:** {result.get('error', 'Unknown error')}")
        
        # File write operations
        if "write_file" in context.tool_results:
            result = context.tool_results["write_file"]
            
            if result.get("success"):
                response_parts.append(f"💾 **Successfully wrote file:** `{result['path']}`")
                response_parts.append(f"• **Size:** {result.get('size', 0)} bytes")
                response_parts.append(f"• **Lines:** {result.get('lines', 0)}")
                
                if result.get("backup_path"):
                    response_parts.append(f"• **Backup created:** `{result['backup_path']}`")
                
                validation = result.get("validation")
                if validation and validation.get("syntax_valid"):
                    response_parts.append("✅ **Syntax validation passed**")
            else:
                response_parts.append(f"❌ **Failed to write file:** {result.get('error', 'Unknown error')}")
        
        return "\n".join(response_parts)
    
    async def _generate_general_response(self, context: ResponseContext) -> str:
        """Generate a general response."""
        response_parts = []
        
        # Acknowledge the request
        response_parts.append("I'll help you with that request.")
        
        # Process any tool results
        for tool_name, result in context.tool_results.items():
            if isinstance(result, dict) and result.get("success"):
                response_parts.append(f"\n✅ **{tool_name.replace('_', ' ').title()}:** Completed successfully")
                
                if "output" in result and result["output"]:
                    response_parts.append(f"```\n{result['output']}\n```")
            elif isinstance(result, dict):
                response_parts.append(f"\n❌ **{tool_name.replace('_', ' ').title()}:** {result.get('error', 'Failed')}")
        
        # Add helpful suggestions
        response_parts.append("\nIs there anything specific you'd like me to explain or help you with next?")
        
        return "\n".join(response_parts)
    
    def _generate_reasoning_chain(self, context: ResponseContext) -> str:
        """Generate a reasoning chain for the response."""
        reasoning_steps = context.analysis.get("reasoning_chain", [])
        if not reasoning_steps:
            return ""
        
        formatted_steps = []
        for i, step in enumerate(reasoning_steps[:4], 1):  # Show first 4 steps
            formatted_steps.append(f"{i}. {step}")
        
        return "\n".join(formatted_steps)
    
    def _extract_code_from_context(self, context: ResponseContext) -> Optional[str]:
        """Extract code from the context."""
        # Look for code in tool results
        if "run_python" in context.tool_results:
            # This would need to be implemented based on how code is stored
            pass
        
        # Look for code blocks in user input
        code_match = re.search(r'```(?:python)?\n(.*?)\n```', context.user_input, re.DOTALL)
        if code_match:
            return code_match.group(1)
        
        return None
    
    def _generate_code_explanation(self, context: ResponseContext) -> str:
        """Generate explanation for code."""
        user_input = context.user_input.lower()
        
        if "factorial" in user_input:
            return "This code implements a recursive factorial function. The factorial of n (n!) is the product of all positive integers less than or equal to n."
        elif "fibonacci" in user_input:
            return "This code generates the Fibonacci sequence, where each number is the sum of the two preceding ones (0, 1, 1, 2, 3, 5, 8, ...)."
        elif "reverse" in user_input:
            return "This code demonstrates two ways to reverse a string: using Python's slice notation ([::-1]) and using a loop to build the reversed string character by character."
        else:
            return "The code demonstrates the requested functionality with clear variable names and proper structure."
    
    def _generate_next_steps(self, context: ResponseContext) -> str:
        """Generate suggestions for next steps."""
        suggestions = [
            "• Test the code with different inputs",
            "• Add error handling for edge cases",
            "• Consider adding documentation/comments"
        ]
        
        if "test" not in context.user_input.lower():
            suggestions.append("• Write unit tests to verify functionality")
        
        return "\n".join(suggestions)
    
    def _analyze_problem(self, context: ResponseContext) -> str:
        """Analyze the problem from the context."""
        if "run_python" in context.tool_results:
            result = context.tool_results["run_python"]
            if not result.get("success"):
                error = result.get("error", "")
                if "SyntaxError" in error:
                    return "The code has syntax errors that prevent it from running."
                elif "NameError" in error:
                    return "There are undefined variables or functions in the code."
                elif "TypeError" in error:
                    return "There's a type mismatch or incorrect function usage."
                else:
                    return f"The code encountered a runtime error: {error}"
        
        return "Analyzing the issue to provide the best solution."
    
    def _generate_detailed_explanation(self, context: ResponseContext) -> str:
        """Generate detailed explanation based on the request."""
        user_input = context.user_input.lower()
        
        if "python" in user_input:
            return "Python is a high-level, interpreted programming language known for its simplicity and readability. It's widely used for web development, data science, automation, and more."
        elif "function" in user_input:
            return "Functions in programming are reusable blocks of code that perform specific tasks. They help organize code, reduce repetition, and make programs more modular."
        elif "algorithm" in user_input:
            return "An algorithm is a step-by-step procedure for solving a problem or completing a task. Good algorithms are efficient, correct, and easy to understand."
        else:
            return "Let me explain this concept in detail with examples and practical applications."
    
    def _generate_additional_context(self, context: ResponseContext) -> str:
        """Generate additional contextual information."""
        return "For more advanced usage, consider exploring related concepts and best practices in the documentation."
