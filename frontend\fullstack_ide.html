<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Full-Stack AI Development IDE</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/theme/monokai.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #1e1e1e;
            color: #d4d4d4;
            height: 100vh;
            overflow: hidden;
        }

        .ide-container {
            display: flex;
            height: 100vh;
        }

        /* Sidebar */
        .sidebar {
            width: 300px;
            background: #252526;
            border-right: 1px solid #3e3e42;
            display: flex;
            flex-direction: column;
        }

        .sidebar-header {
            padding: 15px;
            background: #2d2d30;
            border-bottom: 1px solid #3e3e42;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .sidebar-header h2 {
            font-size: 16px;
            color: #cccccc;
        }

        .sidebar-tabs {
            display: flex;
            background: #2d2d30;
            border-bottom: 1px solid #3e3e42;
        }

        .sidebar-tab {
            flex: 1;
            padding: 8px 12px;
            background: none;
            border: none;
            color: #cccccc;
            cursor: pointer;
            font-size: 12px;
            transition: background 0.2s;
        }

        .sidebar-tab.active {
            background: #37373d;
            color: #ffffff;
        }

        .sidebar-tab:hover {
            background: #37373d;
        }

        .sidebar-content {
            flex: 1;
            overflow-y: auto;
            padding: 10px;
        }

        /* File Explorer */
        .file-explorer {
            font-family: 'Courier New', monospace;
            font-size: 13px;
        }

        .file-item {
            display: flex;
            align-items: center;
            padding: 4px 8px;
            cursor: pointer;
            border-radius: 3px;
            transition: background 0.2s;
        }

        .file-item:hover {
            background: #37373d;
        }

        .file-item.active {
            background: #094771;
        }

        .file-item i {
            margin-right: 8px;
            width: 16px;
            text-align: center;
        }

        .folder-icon {
            color: #dcb67a;
        }

        .file-icon {
            color: #569cd6;
        }

        /* Main Content */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        /* Top Bar */
        .top-bar {
            height: 50px;
            background: #2d2d30;
            border-bottom: 1px solid #3e3e42;
            display: flex;
            align-items: center;
            padding: 0 15px;
            gap: 15px;
        }

        .project-selector {
            background: #3c3c3c;
            border: 1px solid #464647;
            color: #cccccc;
            padding: 6px 12px;
            border-radius: 3px;
            font-size: 13px;
        }

        .action-buttons {
            display: flex;
            gap: 8px;
            margin-left: auto;
        }

        .btn {
            padding: 6px 12px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .btn-primary {
            background: #0e639c;
            color: white;
        }

        .btn-primary:hover {
            background: #1177bb;
        }

        .btn-success {
            background: #107c10;
            color: white;
        }

        .btn-success:hover {
            background: #0f7b0f;
        }

        .btn-secondary {
            background: #5a5a5a;
            color: white;
        }

        .btn-secondary:hover {
            background: #6e6e6e;
        }

        /* Editor Area */
        .editor-area {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .editor-tabs {
            display: flex;
            background: #2d2d30;
            border-bottom: 1px solid #3e3e42;
            overflow-x: auto;
        }

        .editor-tab {
            display: flex;
            align-items: center;
            padding: 8px 15px;
            background: #2d2d30;
            border: none;
            color: #cccccc;
            cursor: pointer;
            font-size: 13px;
            white-space: nowrap;
            border-right: 1px solid #3e3e42;
        }

        .editor-tab.active {
            background: #1e1e1e;
            color: #ffffff;
        }

        .editor-tab:hover {
            background: #37373d;
        }

        .editor-tab .close-btn {
            margin-left: 8px;
            padding: 2px 4px;
            border-radius: 2px;
            opacity: 0.7;
        }

        .editor-tab .close-btn:hover {
            background: #e81123;
            opacity: 1;
        }

        .editor-container {
            flex: 1;
            position: relative;
        }

        .CodeMirror {
            height: 100%;
            font-family: 'Fira Code', 'Courier New', monospace;
            font-size: 14px;
        }

        /* Bottom Panel */
        .bottom-panel {
            height: 250px;
            background: #252526;
            border-top: 1px solid #3e3e42;
            display: flex;
            flex-direction: column;
        }

        .bottom-panel-tabs {
            display: flex;
            background: #2d2d30;
            border-bottom: 1px solid #3e3e42;
        }

        .bottom-panel-tab {
            padding: 8px 15px;
            background: none;
            border: none;
            color: #cccccc;
            cursor: pointer;
            font-size: 12px;
            border-right: 1px solid #3e3e42;
        }

        .bottom-panel-tab.active {
            background: #252526;
            color: #ffffff;
        }

        .bottom-panel-content {
            flex: 1;
            overflow-y: auto;
            padding: 10px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
        }

        /* Terminal */
        .terminal {
            background: #0c0c0c;
            color: #cccccc;
            padding: 10px;
            height: 100%;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
        }

        .terminal-input {
            display: flex;
            align-items: center;
            margin-top: 10px;
        }

        .terminal-prompt {
            color: #569cd6;
            margin-right: 8px;
        }

        .terminal-command {
            flex: 1;
            background: transparent;
            border: none;
            color: #cccccc;
            font-family: inherit;
            font-size: inherit;
            outline: none;
        }

        /* AI Chat */
        .ai-chat {
            display: flex;
            flex-direction: column;
            height: 100%;
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 10px;
        }

        .chat-message {
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 6px;
            animation: fadeIn 0.3s ease-in;
        }

        .chat-message.user {
            background: #094771;
            margin-left: 20px;
        }

        .chat-message.assistant {
            background: #1a472a;
            margin-right: 20px;
        }

        .chat-input-area {
            padding: 10px;
            border-top: 1px solid #3e3e42;
            display: flex;
            gap: 10px;
        }

        .chat-input {
            flex: 1;
            background: #3c3c3c;
            border: 1px solid #464647;
            color: #cccccc;
            padding: 8px 12px;
            border-radius: 3px;
            font-size: 13px;
            resize: none;
        }

        .chat-send {
            background: #0e639c;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 3px;
            cursor: pointer;
        }

        /* Project Generator */
        .project-generator {
            padding: 20px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #cccccc;
            font-size: 13px;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            background: #3c3c3c;
            border: 1px solid #464647;
            color: #cccccc;
            padding: 8px 12px;
            border-radius: 3px;
            font-size: 13px;
        }

        .form-group textarea {
            resize: vertical;
            min-height: 60px;
        }

        /* Status Bar */
        .status-bar {
            height: 25px;
            background: #007acc;
            color: white;
            display: flex;
            align-items: center;
            padding: 0 15px;
            font-size: 12px;
            gap: 20px;
        }

        .status-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        /* Animations */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .sidebar {
                width: 250px;
            }
            
            .bottom-panel {
                height: 200px;
            }
        }

        /* Code Generation Modal */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            z-index: 1000;
        }

        .modal.active {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background: #2d2d30;
            border: 1px solid #3e3e42;
            border-radius: 6px;
            padding: 20px;
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #3e3e42;
        }

        .modal-close {
            background: none;
            border: none;
            color: #cccccc;
            font-size: 20px;
            cursor: pointer;
        }

        .field-list {
            margin-bottom: 15px;
        }

        .field-item {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
            align-items: center;
        }

        .field-item input {
            flex: 1;
        }

        .field-item select {
            width: 120px;
        }

        .remove-field {
            background: #e81123;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 3px;
            cursor: pointer;
        }

        .add-field {
            background: #107c10;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 3px;
            cursor: pointer;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="ide-container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="sidebar-header">
                <i class="fas fa-code"></i>
                <h2>Full-Stack AI IDE</h2>
            </div>
            
            <div class="sidebar-tabs">
                <button class="sidebar-tab active" onclick="switchSidebarTab('explorer')">
                    <i class="fas fa-folder"></i> Explorer
                </button>
                <button class="sidebar-tab" onclick="switchSidebarTab('generator')">
                    <i class="fas fa-magic"></i> Generate
                </button>
                <button class="sidebar-tab" onclick="switchSidebarTab('git')">
                    <i class="fab fa-git-alt"></i> Git
                </button>
            </div>
            
            <div class="sidebar-content">
                <!-- File Explorer -->
                <div id="explorer-content" class="tab-content active">
                    <div class="file-explorer" id="fileExplorer">
                        <div class="file-item" onclick="toggleFolder(this)">
                            <i class="fas fa-folder folder-icon"></i>
                            <span>my-project</span>
                        </div>
                        <div class="file-item" style="margin-left: 20px;" onclick="openFile('src/App.js')">
                            <i class="fas fa-file-code file-icon"></i>
                            <span>App.js</span>
                        </div>
                        <div class="file-item" style="margin-left: 20px;" onclick="openFile('src/index.js')">
                            <i class="fas fa-file-code file-icon"></i>
                            <span>index.js</span>
                        </div>
                        <div class="file-item" style="margin-left: 20px;" onclick="openFile('package.json')">
                            <i class="fas fa-file-code file-icon"></i>
                            <span>package.json</span>
                        </div>
                    </div>
                </div>
                
                <!-- Project Generator -->
                <div id="generator-content" class="tab-content" style="display: none;">
                    <div class="project-generator">
                        <h3 style="margin-bottom: 20px; color: #cccccc;">Create New Project</h3>
                        
                        <div class="form-group">
                            <label>Project Name</label>
                            <input type="text" id="projectName" placeholder="my-awesome-app">
                        </div>
                        
                        <div class="form-group">
                            <label>Project Type</label>
                            <select id="projectType">
                                <option value="react">React App</option>
                                <option value="vue">Vue App</option>
                                <option value="angular">Angular App</option>
                                <option value="node_api">Node.js API</option>
                                <option value="python_api">Python FastAPI</option>
                                <option value="fullstack">Full-Stack App</option>
                                <option value="static">Static Website</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label>Description</label>
                            <textarea id="projectDescription" placeholder="Describe your project..."></textarea>
                        </div>
                        
                        <div class="form-group">
                            <label>Database (Optional)</label>
                            <select id="projectDatabase">
                                <option value="">None</option>
                                <option value="postgresql">PostgreSQL</option>
                                <option value="mysql">MySQL</option>
                                <option value="mongodb">MongoDB</option>
                                <option value="sqlite">SQLite</option>
                            </select>
                        </div>
                        
                        <button class="btn btn-primary" onclick="generateProject()">
                            <i class="fas fa-magic"></i> Generate Project
                        </button>
                        
                        <hr style="margin: 20px 0; border-color: #3e3e42;">
                        
                        <h3 style="margin-bottom: 15px; color: #cccccc;">Generate CRUD</h3>
                        <button class="btn btn-success" onclick="openCrudGenerator()">
                            <i class="fas fa-plus"></i> Create CRUD System
                        </button>
                    </div>
                </div>
                
                <!-- Git Panel -->
                <div id="git-content" class="tab-content" style="display: none;">
                    <div style="padding: 15px;">
                        <h3 style="margin-bottom: 15px; color: #cccccc;">Git Status</h3>
                        <div style="font-family: 'Courier New', monospace; font-size: 12px; color: #569cd6;">
                            <div>• Modified: 3 files</div>
                            <div>• Staged: 1 file</div>
                            <div>• Untracked: 2 files</div>
                        </div>
                        
                        <div style="margin-top: 20px;">
                            <button class="btn btn-primary" style="width: 100%; margin-bottom: 8px;">
                                <i class="fas fa-plus"></i> Stage All
                            </button>
                            <button class="btn btn-success" style="width: 100%; margin-bottom: 8px;">
                                <i class="fas fa-check"></i> Commit
                            </button>
                            <button class="btn btn-secondary" style="width: 100%;">
                                <i class="fas fa-upload"></i> Push
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Top Bar -->
            <div class="top-bar">
                <select class="project-selector">
                    <option>my-project</option>
                    <option>Create New Project...</option>
                </select>
                
                <div class="action-buttons">
                    <button class="btn btn-secondary" onclick="runProject()">
                        <i class="fas fa-play"></i> Run
                    </button>
                    <button class="btn btn-primary" onclick="buildProject()">
                        <i class="fas fa-hammer"></i> Build
                    </button>
                    <button class="btn btn-success" onclick="deployProject()">
                        <i class="fas fa-rocket"></i> Deploy
                    </button>
                </div>
            </div>

            <!-- Editor Area -->
            <div class="editor-area">
                <div class="editor-tabs" id="editorTabs">
                    <button class="editor-tab active" onclick="switchEditorTab('App.js')">
                        <i class="fab fa-js-square"></i>
                        App.js
                        <span class="close-btn" onclick="closeTab(event, 'App.js')">×</span>
                    </button>
                </div>
                
                <div class="editor-container">
                    <textarea id="codeEditor"></textarea>
                </div>
            </div>

            <!-- Bottom Panel -->
            <div class="bottom-panel">
                <div class="bottom-panel-tabs">
                    <button class="bottom-panel-tab active" onclick="switchBottomTab('terminal')">
                        <i class="fas fa-terminal"></i> Terminal
                    </button>
                    <button class="bottom-panel-tab" onclick="switchBottomTab('ai-chat')">
                        <i class="fas fa-robot"></i> AI Assistant
                    </button>
                    <button class="bottom-panel-tab" onclick="switchBottomTab('output')">
                        <i class="fas fa-list"></i> Output
                    </button>
                    <button class="bottom-panel-tab" onclick="switchBottomTab('problems')">
                        <i class="fas fa-exclamation-triangle"></i> Problems
                    </button>
                </div>
                
                <div class="bottom-panel-content">
                    <!-- Terminal -->
                    <div id="terminal-content" class="tab-content active">
                        <div class="terminal" id="terminal">
                            <div>Full-Stack AI IDE Terminal v1.0.0</div>
                            <div>Type 'help' for available commands</div>
                            <div class="terminal-input">
                                <span class="terminal-prompt">$</span>
                                <input type="text" class="terminal-command" id="terminalInput" placeholder="Enter command...">
                            </div>
                        </div>
                    </div>
                    
                    <!-- AI Chat -->
                    <div id="ai-chat-content" class="tab-content" style="display: none;">
                        <div class="ai-chat">
                            <div class="chat-messages" id="chatMessages">
                                <div class="chat-message assistant">
                                    <strong>🤖 AI Assistant:</strong> Hello! I'm your full-stack development AI assistant. I can help you with:
                                    <br>• Creating React/Vue/Angular components
                                    <br>• Building APIs with Node.js/Python
                                    <br>• Database design and queries
                                    <br>• Debugging and optimization
                                    <br>• Deployment and DevOps
                                    <br><br>What would you like to build today?
                                </div>
                            </div>
                            <div class="chat-input-area">
                                <textarea class="chat-input" id="chatInput" placeholder="Ask me anything about development..." rows="2"></textarea>
                                <button class="chat-send" onclick="sendChatMessage()">
                                    <i class="fas fa-paper-plane"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Output -->
                    <div id="output-content" class="tab-content" style="display: none;">
                        <div id="outputLog">
                            <div style="color: #569cd6;">[INFO] Project initialized successfully</div>
                            <div style="color: #4ec9b0;">[SUCCESS] Dependencies installed</div>
                            <div style="color: #dcdcaa;">[BUILD] Compiling...</div>
                        </div>
                    </div>
                    
                    <!-- Problems -->
                    <div id="problems-content" class="tab-content" style="display: none;">
                        <div id="problemsList">
                            <div style="color: #f44747;">❌ Error: Missing semicolon at line 23</div>
                            <div style="color: #ff8c00;">⚠️ Warning: Unused variable 'data' at line 15</div>
                            <div style="color: #569cd6;">ℹ️ Info: Consider using const instead of let</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Status Bar -->
    <div class="status-bar">
        <div class="status-item">
            <i class="fas fa-circle" style="color: #4ec9b0;"></i>
            <span>Connected</span>
        </div>
        <div class="status-item">
            <i class="fab fa-js-square"></i>
            <span>JavaScript</span>
        </div>
        <div class="status-item">
            <i class="fas fa-code-branch"></i>
            <span>main</span>
        </div>
        <div class="status-item">
            <span>Ln 1, Col 1</span>
        </div>
    </div>

    <!-- CRUD Generator Modal -->
    <div id="crudModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Generate CRUD System</h3>
                <button class="modal-close" onclick="closeCrudGenerator()">×</button>
            </div>
            
            <div class="form-group">
                <label>Entity Name</label>
                <input type="text" id="entityName" placeholder="User, Product, Order...">
            </div>
            
            <div class="form-group">
                <label>Framework</label>
                <select id="crudFramework">
                    <option value="react_fastapi">React + FastAPI</option>
                    <option value="vue_node">Vue + Node.js</option>
                    <option value="angular_spring">Angular + Spring</option>
                    <option value="react">React Only</option>
                    <option value="fastapi">FastAPI Only</option>
                </select>
            </div>
            
            <div class="form-group">
                <label>Fields</label>
                <div class="field-list" id="fieldList">
                    <div class="field-item">
                        <input type="text" placeholder="Field name" value="name">
                        <select>
                            <option value="string">String</option>
                            <option value="number">Number</option>
                            <option value="boolean">Boolean</option>
                            <option value="date">Date</option>
                            <option value="text">Text</option>
                        </select>
                        <button class="remove-field" onclick="removeField(this)">×</button>
                    </div>
                </div>
                <button class="add-field" onclick="addField()">+ Add Field</button>
            </div>
            
            <div style="display: flex; gap: 10px; justify-content: flex-end;">
                <button class="btn btn-secondary" onclick="closeCrudGenerator()">Cancel</button>
                <button class="btn btn-primary" onclick="generateCrud()">Generate CRUD</button>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/javascript/javascript.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/python/python.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/xml/xml.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/css/css.min.js"></script>
    
    <script>
        // Initialize CodeMirror
        let editor = CodeMirror.fromTextArea(document.getElementById('codeEditor'), {
            lineNumbers: true,
            mode: 'javascript',
            theme: 'monokai',
            indentUnit: 2,
            lineWrapping: true,
            autoCloseBrackets: true,
            matchBrackets: true,
            foldGutter: true,
            gutters: ["CodeMirror-linenumbers", "CodeMirror-foldgutter"]
        });

        // Sample code
        editor.setValue(`import React, { useState, useEffect } from 'react';
import './App.css';

function App() {
  const [count, setCount] = useState(0);
  const [data, setData] = useState([]);

  useEffect(() => {
    // Fetch data from API
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      const response = await fetch('/api/data');
      const result = await response.json();
      setData(result);
    } catch (error) {
      console.error('Error fetching data:', error);
    }
  };

  return (
    <div className="App">
      <header className="App-header">
        <h1>Full-Stack AI IDE</h1>
        <p>Build amazing applications with AI assistance!</p>
        <button onClick={() => setCount(count + 1)}>
          Count: {count}
        </button>
      </header>
      
      <main>
        <section className="data-section">
          <h2>Data from API</h2>
          <ul>
            {data.map(item => (
              <li key={item.id}>{item.name}</li>
            ))}
          </ul>
        </section>
      </main>
    </div>
  );
}

export default App;`);

        // Global variables
        let openTabs = ['App.js'];
        let activeTab = 'App.js';
        let chatHistory = [];

        // Sidebar tab switching
        function switchSidebarTab(tabName) {
            // Update tab buttons
            document.querySelectorAll('.sidebar-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // Update content
            document.querySelectorAll('#explorer-content, #generator-content, #git-content').forEach(content => {
                content.style.display = 'none';
            });
            document.getElementById(tabName + '-content').style.display = 'block';
        }

        // Bottom panel tab switching
        function switchBottomTab(tabName) {
            // Update tab buttons
            document.querySelectorAll('.bottom-panel-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // Update content
            document.querySelectorAll('#terminal-content, #ai-chat-content, #output-content, #problems-content').forEach(content => {
                content.style.display = 'none';
            });
            document.getElementById(tabName + '-content').style.display = 'block';
        }

        // File operations
        function openFile(filename) {
            if (!openTabs.includes(filename)) {
                openTabs.push(filename);
                addEditorTab(filename);
            }
            switchEditorTab(filename);
        }

        function addEditorTab(filename) {
            const tabsContainer = document.getElementById('editorTabs');
            const tab = document.createElement('button');
            tab.className = 'editor-tab';
            tab.onclick = () => switchEditorTab(filename);
            
            const icon = getFileIcon(filename);
            tab.innerHTML = `
                ${icon}
                ${filename}
                <span class="close-btn" onclick="closeTab(event, '${filename}')">×</span>
            `;
            
            tabsContainer.appendChild(tab);
        }

        function getFileIcon(filename) {
            const ext = filename.split('.').pop();
            switch(ext) {
                case 'js': case 'jsx': return '<i class="fab fa-js-square"></i>';
                case 'ts': case 'tsx': return '<i class="fab fa-js-square" style="color: #3178c6;"></i>';
                case 'py': return '<i class="fab fa-python"></i>';
                case 'html': return '<i class="fab fa-html5"></i>';
                case 'css': return '<i class="fab fa-css3-alt"></i>';
                case 'json': return '<i class="fas fa-file-code"></i>';
                default: return '<i class="fas fa-file"></i>';
            }
        }

        function switchEditorTab(filename) {
            // Update tab appearance
            document.querySelectorAll('.editor-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            event.target.classList.add('active');
            
            activeTab = filename;
            
            // Load file content (simulated)
            loadFileContent(filename);
        }

        function loadFileContent(filename) {
            // Simulate loading different file contents
            const contents = {
                'App.js': editor.getValue(), // Keep current content for App.js
                'index.js': `import React from 'react';
import ReactDOM from 'react-dom/client';
import './index.css';
import App from './App';

const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);`,
                'package.json': `{
  "name": "my-project",
  "version": "0.1.0",
  "private": true,
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "axios": "^1.3.0"
  },
  "scripts": {
    "start": "react-scripts start",
    "build": "react-scripts build",
    "test": "react-scripts test",
    "eject": "react-scripts eject"
  }
}`
            };
            
            if (contents[filename]) {
                editor.setValue(contents[filename]);
                
                // Set appropriate mode
                const mode = getEditorMode(filename);
                editor.setOption('mode', mode);
            }
        }

        function getEditorMode(filename) {
            const ext = filename.split('.').pop();
            switch(ext) {
                case 'js': case 'jsx': return 'javascript';
                case 'ts': case 'tsx': return 'javascript';
                case 'py': return 'python';
                case 'html': return 'xml';
                case 'css': return 'css';
                case 'json': return 'javascript';
                default: return 'text';
            }
        }

        function closeTab(event, filename) {
            event.stopPropagation();
            
            const index = openTabs.indexOf(filename);
            if (index > -1) {
                openTabs.splice(index, 1);
                
                // Remove tab element
                event.target.parentElement.remove();
                
                // Switch to another tab if this was active
                if (activeTab === filename && openTabs.length > 0) {
                    switchEditorTab(openTabs[0]);
                }
            }
        }

        // Terminal functionality
        document.getElementById('terminalInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                const command = this.value;
                executeCommand(command);
                this.value = '';
            }
        });

        function executeCommand(command) {
            const terminal = document.getElementById('terminal');
            
            // Add command to terminal
            const commandDiv = document.createElement('div');
            commandDiv.innerHTML = `<span class="terminal-prompt">$</span> ${command}`;
            terminal.appendChild(commandDiv);
            
            // Simulate command execution
            setTimeout(() => {
                const output = getCommandOutput(command);
                const outputDiv = document.createElement('div');
                outputDiv.innerHTML = output;
                terminal.appendChild(outputDiv);
                
                // Add new input line
                const newInput = document.createElement('div');
                newInput.className = 'terminal-input';
                newInput.innerHTML = `
                    <span class="terminal-prompt">$</span>
                    <input type="text" class="terminal-command" placeholder="Enter command...">
                `;
                terminal.appendChild(newInput);
                
                // Focus new input
                const newInputField = newInput.querySelector('input');
                newInputField.focus();
                newInputField.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        executeCommand(this.value);
                        this.value = '';
                    }
                });
                
                terminal.scrollTop = terminal.scrollHeight;
            }, 500);
        }

        function getCommandOutput(command) {
            const commands = {
                'help': `Available commands:
  npm start    - Start development server
  npm build    - Build for production
  npm test     - Run tests
  git status   - Show git status
  ls           - List files
  clear        - Clear terminal`,
                'npm start': 'Starting development server on http://localhost:3000...',
                'npm build': 'Building for production... Build completed successfully!',
                'npm test': 'Running tests... All tests passed!',
                'git status': 'On branch main\nYour branch is up to date with origin/main',
                'ls': 'src/  public/  package.json  README.md',
                'clear': ''
            };
            
            return commands[command] || `Command not found: ${command}`;
        }

        // AI Chat functionality
        function sendChatMessage() {
            const input = document.getElementById('chatInput');
            const message = input.value.trim();
            
            if (!message) return;
            
            // Add user message
            addChatMessage('user', message);
            
            // Clear input
            input.value = '';
            
            // Simulate AI response
            setTimeout(() => {
                const response = generateAIResponse(message);
                addChatMessage('assistant', response);
            }, 1000);
        }

        function addChatMessage(role, content) {
            const messagesContainer = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `chat-message ${role}`;
            
            const icon = role === 'user' ? '👤' : '🤖';
            const sender = role === 'user' ? 'You' : 'AI Assistant';
            
            messageDiv.innerHTML = `<strong>${icon} ${sender}:</strong> ${content}`;
            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        function generateAIResponse(message) {
            const responses = {
                'create component': `I'll help you create a React component! Here's a template:

\`\`\`jsx
import React from 'react';

const MyComponent = ({ title, children }) => {
  return (
    <div className="my-component">
      <h2>{title}</h2>
      {children}
    </div>
  );
};

export default MyComponent;
\`\`\`

Would you like me to customize this component for your specific needs?`,
                
                'api endpoint': `I can help you create an API endpoint! Here's a FastAPI example:

\`\`\`python
from fastapi import APIRouter

router = APIRouter()

@router.get("/users")
async def get_users():
    return {"users": []}

@router.post("/users")
async def create_user(user: UserCreate):
    # Create user logic
    return {"message": "User created"}
\`\`\`

What kind of endpoint would you like to create?`,
                
                'database': `I can help with database design! Here are some options:

• **PostgreSQL** - Great for complex queries and ACID compliance
• **MongoDB** - Perfect for flexible document storage
• **SQLite** - Ideal for development and small applications

What type of data will you be storing?`
            };
            
            // Simple keyword matching
            for (const [keyword, response] of Object.entries(responses)) {
                if (message.toLowerCase().includes(keyword)) {
                    return response;
                }
            }
            
            return `I understand you want help with: "${message}". I can assist with:

• Creating React/Vue/Angular components
• Building REST APIs with Node.js/Python
• Database design and queries
• Code optimization and debugging
• Deployment strategies

Could you be more specific about what you'd like to build?`;
        }

        // Project actions
        function runProject() {
            addToOutput('[INFO] Starting development server...', '#569cd6');
            setTimeout(() => {
                addToOutput('[SUCCESS] Server running on http://localhost:3000', '#4ec9b0');
            }, 1500);
        }

        function buildProject() {
            addToOutput('[INFO] Building project for production...', '#569cd6');
            setTimeout(() => {
                addToOutput('[SUCCESS] Build completed successfully!', '#4ec9b0');
            }, 2000);
        }

        function deployProject() {
            addToOutput('[INFO] Deploying to production...', '#569cd6');
            setTimeout(() => {
                addToOutput('[SUCCESS] Deployment completed!', '#4ec9b0');
            }, 3000);
        }

        function addToOutput(message, color = '#cccccc') {
            const output = document.getElementById('outputLog');
            const div = document.createElement('div');
            div.style.color = color;
            div.textContent = message;
            output.appendChild(div);
            output.scrollTop = output.scrollHeight;
        }

        // Project generation
        function generateProject() {
            const name = document.getElementById('projectName').value;
            const type = document.getElementById('projectType').value;
            const description = document.getElementById('projectDescription').value;
            const database = document.getElementById('projectDatabase').value;
            
            if (!name) {
                alert('Please enter a project name');
                return;
            }
            
            addToOutput(`[INFO] Generating ${type} project: ${name}`, '#569cd6');
            
            setTimeout(() => {
                addToOutput('[INFO] Creating project structure...', '#569cd6');
                setTimeout(() => {
                    addToOutput('[INFO] Installing dependencies...', '#569cd6');
                    setTimeout(() => {
                        addToOutput('[SUCCESS] Project generated successfully!', '#4ec9b0');
                        addToOutput(`[INFO] Run 'cd ${name} && npm start' to begin development`, '#dcdcaa');
                    }, 1500);
                }, 1000);
            }, 500);
        }

        // CRUD Generator
        function openCrudGenerator() {
            document.getElementById('crudModal').classList.add('active');
        }

        function closeCrudGenerator() {
            document.getElementById('crudModal').classList.remove('active');
        }

        function addField() {
            const fieldList = document.getElementById('fieldList');
            const fieldItem = document.createElement('div');
            fieldItem.className = 'field-item';
            fieldItem.innerHTML = `
                <input type="text" placeholder="Field name">
                <select>
                    <option value="string">String</option>
                    <option value="number">Number</option>
                    <option value="boolean">Boolean</option>
                    <option value="date">Date</option>
                    <option value="text">Text</option>
                </select>
                <button class="remove-field" onclick="removeField(this)">×</button>
            `;
            fieldList.appendChild(fieldItem);
        }

        function removeField(button) {
            button.parentElement.remove();
        }

        function generateCrud() {
            const entityName = document.getElementById('entityName').value;
            const framework = document.getElementById('crudFramework').value;
            
            if (!entityName) {
                alert('Please enter an entity name');
                return;
            }
            
            const fields = [];
            document.querySelectorAll('.field-item').forEach(item => {
                const name = item.querySelector('input').value;
                const type = item.querySelector('select').value;
                if (name) {
                    fields.push({ name, type });
                }
            });
            
            addToOutput(`[INFO] Generating CRUD system for ${entityName}...`, '#569cd6');
            
            setTimeout(() => {
                addToOutput('[INFO] Creating React components...', '#569cd6');
                setTimeout(() => {
                    addToOutput('[INFO] Generating API endpoints...', '#569cd6');
                    setTimeout(() => {
                        addToOutput('[INFO] Creating database models...', '#569cd6');
                        setTimeout(() => {
                            addToOutput('[SUCCESS] CRUD system generated successfully!', '#4ec9b0');
                            addToOutput(`[INFO] Files created: ${entityName}Manager.js, ${entityName.toLowerCase()}_api.py, ${entityName.toLowerCase()}_model.py`, '#dcdcaa');
                        }, 800);
                    }, 800);
                }, 800);
            }, 500);
            
            closeCrudGenerator();
        }

        // Chat input handling
        document.getElementById('chatInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendChatMessage();
            }
        });

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            // Focus on editor
            editor.focus();
            
            // Add welcome message to output
            addToOutput('[INFO] Full-Stack AI IDE initialized', '#569cd6');
            addToOutput('[INFO] Ready for development!', '#4ec9b0');
        });
    </script>
</body>
</html>
