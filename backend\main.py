# backend/main.py
from fastapi import FastAPI
from pydantic import BaseModel
from .agent import agent_step

app = FastAPI()

SYSTEM_PROMPT = "You are an augmented programming assistant. When you need to run code or access project files, respond with the TOOLCALL format exactly as described. Tools available: run_python, read_file, write_file."

class ChatRequest(BaseModel):
    messages: list  # list of {role: 'user'|'assistant'|'system', content: str}
    input: str

class ChatResponse(BaseModel):
    reply: str

@app.post('/chat', response_model=ChatResponse)
async def chat(req: ChatRequest):
    # history = req.messages  # trust the client to provide history or store server-side
    history = req.messages
    reply = agent_step(SYSTEM_PROMPT, history, req.input)
    return {"reply": reply}

if __name__ == '__main__':
    import uvicorn
    uvicorn.run(app, host='0.0.0.0', port=8000)
