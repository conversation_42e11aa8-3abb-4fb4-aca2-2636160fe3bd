#!/usr/bin/env python3
"""
Simple test script to verify the agent setup works.
This tests the tools without requiring the full FastAPI server.
"""

import sys
import os
from pathlib import Path

# Add backend to path so we can import modules
backend_path = Path(__file__).parent / "backend"
sys.path.insert(0, str(backend_path))

def test_tools():
    """Test the basic tools functionality."""
    print("Testing tools...")
    
    try:
        from tools import run_python, read_file, write_file
        
        # Test Python execution
        print("\n1. Testing Python execution:")
        result = run_python("print('Hello from Python!')")
        print(f"Result: {result}")
        
        # Test file writing
        print("\n2. Testing file writing:")
        result = write_file("test_output.txt", "Hello from the agent!")
        print(f"Write result: {result}")
        
        # Test file reading
        print("\n3. Testing file reading:")
        result = read_file("test_output.txt")
        print(f"Read result: {result}")
        
        # Test Python with calculation
        print("\n4. Testing Python calculation:")
        result = run_python("""
import math
result = math.sqrt(16) + 2 * 3
print(f"Calculation result: {result}")
""")
        print(f"Result: {result}")
        
        print("\n✅ All tool tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Tool test failed: {e}")
        return False

def test_agent():
    """Test the agent functionality (requires OpenAI API key)."""
    print("\nTesting agent...")
    
    if not os.environ.get("OPENAI_API_KEY"):
        print("⚠️  OPENAI_API_KEY not set. Skipping agent test.")
        print("Set your API key to test the full agent functionality.")
        return True
    
    try:
        from agent import agent_step
        
        system_prompt = "You are a helpful assistant. When asked to run code, use the TOOLCALL format."
        history = []
        user_input = "Calculate 2 + 2 using Python"
        
        print(f"User input: {user_input}")
        result = agent_step(system_prompt, history, user_input)
        print(f"Agent response: {result}")
        
        print("\n✅ Agent test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Agent test failed: {e}")
        return False

def main():
    print("Augmented Agent Test Suite")
    print("=" * 40)
    
    # Load .env file if it exists
    env_file = Path(__file__).parent / ".env"
    if env_file.exists():
        print("Loading .env file...")
        with open(env_file) as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    os.environ[key] = value
    
    success = True
    
    # Test tools
    if not test_tools():
        success = False
    
    # Test agent (if API key is available)
    if not test_agent():
        success = False
    
    if success:
        print("\n🎉 All tests completed successfully!")
        print("\nYour augmented agent is ready to use!")
        print("Run 'python run_backend.py' to start the server.")
    else:
        print("\n❌ Some tests failed. Please check the setup.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
