<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>True Claude Agent - Exactly Like Me</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .chat-container {
            width: 90%;
            max-width: 1000px;
            height: 90vh;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.2);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .chat-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            position: relative;
        }

        .chat-header h1 {
            font-size: 24px;
            margin-bottom: 5px;
        }

        .chat-header .subtitle {
            font-size: 14px;
            opacity: 0.9;
        }

        .status-indicator {
            position: absolute;
            top: 20px;
            right: 20px;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 12px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #4ade80;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            background: #f8fafc;
        }

        .message {
            margin-bottom: 20px;
            animation: slideIn 0.3s ease-out;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .message-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 8px;
        }

        .avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            font-weight: bold;
        }

        .user-avatar {
            background: #3b82f6;
            color: white;
        }

        .claude-avatar {
            background: #10b981;
            color: white;
        }

        .message-info {
            flex: 1;
        }

        .sender-name {
            font-weight: 600;
            color: #1f2937;
        }

        .message-time {
            font-size: 11px;
            color: #6b7280;
        }

        .message-content {
            background: white;
            padding: 16px 20px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            line-height: 1.6;
            position: relative;
        }

        .user-message .message-content {
            background: #eff6ff;
            border-left: 4px solid #3b82f6;
        }

        .claude-message .message-content {
            background: white;
            border-left: 4px solid #10b981;
        }

        .reasoning-section {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 8px;
            padding: 12px;
            margin: 10px 0;
            font-size: 13px;
        }

        .reasoning-title {
            font-weight: bold;
            color: #92400e;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .tool-usage {
            background: #e0f2fe;
            border: 1px solid #0284c7;
            border-radius: 8px;
            padding: 12px;
            margin: 10px 0;
            font-size: 13px;
        }

        .tool-title {
            font-weight: bold;
            color: #0c4a6e;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .confidence-meter {
            display: flex;
            align-items: center;
            gap: 8px;
            margin: 10px 0;
            font-size: 12px;
        }

        .confidence-bar {
            flex: 1;
            height: 6px;
            background: #e5e7eb;
            border-radius: 3px;
            overflow: hidden;
        }

        .confidence-fill {
            height: 100%;
            background: linear-gradient(90deg, #ef4444, #f59e0b, #10b981);
            transition: width 0.3s ease;
        }

        .chat-input-area {
            padding: 20px;
            background: white;
            border-top: 1px solid #e5e7eb;
        }

        .input-container {
            display: flex;
            gap: 12px;
            align-items: flex-end;
        }

        .chat-input {
            flex: 1;
            min-height: 50px;
            max-height: 120px;
            padding: 12px 16px;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            resize: none;
            font-family: inherit;
            font-size: 14px;
            line-height: 1.4;
            transition: border-color 0.2s;
        }

        .chat-input:focus {
            outline: none;
            border-color: #667eea;
        }

        .send-button {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 12px;
            cursor: pointer;
            font-size: 18px;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .send-button:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
        }

        .send-button:disabled {
            background: #9ca3af;
            cursor: not-allowed;
            transform: none;
        }

        .thinking-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #6b7280;
            font-style: italic;
            margin: 10px 0;
        }

        .thinking-dots {
            display: flex;
            gap: 4px;
        }

        .thinking-dot {
            width: 6px;
            height: 6px;
            background: #6b7280;
            border-radius: 50%;
            animation: thinking 1.4s infinite ease-in-out;
        }

        .thinking-dot:nth-child(1) { animation-delay: -0.32s; }
        .thinking-dot:nth-child(2) { animation-delay: -0.16s; }

        @keyframes thinking {
            0%, 80%, 100% {
                transform: scale(0);
                opacity: 0.5;
            }
            40% {
                transform: scale(1);
                opacity: 1;
            }
        }

        .welcome-message {
            text-align: center;
            color: #6b7280;
            padding: 40px 20px;
        }

        .welcome-message h2 {
            color: #1f2937;
            margin-bottom: 10px;
        }

        .capabilities {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .capability {
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
        }

        .capability i {
            font-size: 24px;
            color: #667eea;
            margin-bottom: 8px;
        }

        .capability h3 {
            font-size: 14px;
            color: #1f2937;
            margin-bottom: 4px;
        }

        .capability p {
            font-size: 12px;
            color: #6b7280;
        }

        code {
            background: #f3f4f6;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
        }

        pre {
            background: #1f2937;
            color: #e5e7eb;
            padding: 16px;
            border-radius: 8px;
            overflow-x: auto;
            margin: 10px 0;
            font-size: 13px;
        }

        .error-message {
            background: #fef2f2;
            border: 1px solid #fca5a5;
            color: #dc2626;
            padding: 12px;
            border-radius: 8px;
            margin: 10px 0;
        }

        @media (max-width: 768px) {
            .chat-container {
                width: 95%;
                height: 95vh;
                border-radius: 12px;
            }
            
            .capabilities {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            <h1><i class="fas fa-robot"></i> True Claude Agent</h1>
            <div class="subtitle">An AI agent that works exactly like Claude</div>
            <div class="status-indicator">
                <div class="status-dot"></div>
                <span id="statusText">Ready</span>
            </div>
        </div>

        <div class="chat-messages" id="chatMessages">
            <div class="welcome-message">
                <h2>👋 Hello! I'm a True Claude Agent</h2>
                <p>I work exactly like Claude, with the same reasoning patterns, tool usage, and communication style.</p>
                
                <div class="capabilities">
                    <div class="capability">
                        <i class="fas fa-brain"></i>
                        <h3>Authentic Reasoning</h3>
                        <p>I think and analyze problems exactly like Claude</p>
                    </div>
                    <div class="capability">
                        <i class="fas fa-tools"></i>
                        <h3>Strategic Tool Usage</h3>
                        <p>I use tools in the same strategic way Claude does</p>
                    </div>
                    <div class="capability">
                        <i class="fas fa-comments"></i>
                        <h3>Claude's Communication</h3>
                        <p>I explain and respond just like Claude</p>
                    </div>
                    <div class="capability">
                        <i class="fas fa-code"></i>
                        <h3>Programming Help</h3>
                        <p>I can help with coding, debugging, and development</p>
                    </div>
                    <div class="capability">
                        <i class="fas fa-graduation-cap"></i>
                        <h3>Educational Explanations</h3>
                        <p>I provide clear, detailed explanations</p>
                    </div>
                    <div class="capability">
                        <i class="fas fa-lightbulb"></i>
                        <h3>Problem Solving</h3>
                        <p>I approach problems systematically like Claude</p>
                    </div>
                </div>
                
                <p><strong>Try asking me anything!</strong> I'll respond with the same depth and helpfulness as Claude.</p>
            </div>
        </div>

        <div class="chat-input-area">
            <div class="input-container">
                <textarea 
                    id="chatInput" 
                    class="chat-input" 
                    placeholder="Ask me anything... I'll respond exactly like Claude!"
                    rows="2"
                ></textarea>
                <button id="sendButton" class="send-button">
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>
        </div>
    </div>

    <script>
        let conversationId = null;
        let isThinking = false;

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('chatInput').focus();
            checkBackendStatus();
        });

        // Check backend status
        async function checkBackendStatus() {
            try {
                const response = await fetch('http://localhost:8000/');
                if (response.ok) {
                    document.getElementById('statusText').textContent = 'Connected';
                } else {
                    document.getElementById('statusText').textContent = 'Backend offline (demo mode)';
                }
            } catch (error) {
                document.getElementById('statusText').textContent = 'Backend offline (demo mode)';
            }
        }

        // Send message
        async function sendMessage() {
            const input = document.getElementById('chatInput');
            const sendButton = document.getElementById('sendButton');
            const message = input.value.trim();

            if (!message || isThinking) return;

            // Add user message
            addMessage('user', message);
            
            // Clear input and disable
            input.value = '';
            isThinking = true;
            sendButton.disabled = true;

            // Show thinking indicator
            showThinking();

            try {
                // Try to call real backend
                const response = await fetch('http://localhost:8000/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        message: message,
                        conversation_id: conversationId,
                        user_preferences: {}
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    conversationId = data.conversation_id;
                    
                    hideThinking();
                    addClaudeMessage(data);
                } else {
                    throw new Error('Backend unavailable');
                }
            } catch (error) {
                // Fall back to demo mode
                hideThinking();
                const demoResponse = generateDemoResponse(message);
                addClaudeMessage(demoResponse);
            }

            // Re-enable input
            isThinking = false;
            sendButton.disabled = false;
            input.focus();
        }

        // Add user message
        function addMessage(role, content) {
            const messagesContainer = document.getElementById('chatMessages');
            
            // Remove welcome message if it exists
            const welcomeMessage = messagesContainer.querySelector('.welcome-message');
            if (welcomeMessage) {
                welcomeMessage.remove();
            }

            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${role}-message`;

            const avatar = role === 'user' ? 
                '<i class="fas fa-user"></i>' : 
                '<i class="fas fa-robot"></i>';
            
            const avatarClass = role === 'user' ? 'user-avatar' : 'claude-avatar';
            const senderName = role === 'user' ? 'You' : 'True Claude Agent';

            messageDiv.innerHTML = `
                <div class="message-header">
                    <div class="avatar ${avatarClass}">
                        ${avatar}
                    </div>
                    <div class="message-info">
                        <div class="sender-name">${senderName}</div>
                        <div class="message-time">${new Date().toLocaleTimeString()}</div>
                    </div>
                </div>
                <div class="message-content">
                    ${formatContent(content)}
                </div>
            `;

            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        // Add Claude message with reasoning
        function addClaudeMessage(data) {
            const messagesContainer = document.getElementById('chatMessages');
            
            // Remove welcome message if it exists
            const welcomeMessage = messagesContainer.querySelector('.welcome-message');
            if (welcomeMessage) {
                welcomeMessage.remove();
            }

            const messageDiv = document.createElement('div');
            messageDiv.className = 'message claude-message';

            let reasoningSection = '';
            if (data.reasoning_chain && data.reasoning_chain.length > 0) {
                const keyInsights = data.reasoning_chain
                    .filter(step => step.confidence > 0.7)
                    .slice(0, 3)
                    .map(step => `• ${step.description}`)
                    .join('<br>');
                
                if (keyInsights) {
                    reasoningSection = `
                        <div class="reasoning-section">
                            <div class="reasoning-title">
                                <i class="fas fa-brain"></i>
                                My Reasoning Process
                            </div>
                            ${keyInsights}
                        </div>
                    `;
                }
            }

            let toolSection = '';
            if (data.tool_usage && data.tool_usage.length > 0) {
                const toolList = data.tool_usage
                    .slice(0, 3)
                    .map(tool => `• ${tool.tool.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}: ${tool.reasoning}`)
                    .join('<br>');
                
                toolSection = `
                    <div class="tool-usage">
                        <div class="tool-title">
                            <i class="fas fa-tools"></i>
                            Tools Used
                        </div>
                        ${toolList}
                    </div>
                `;
            }

            const confidencePercent = Math.round((data.confidence || 0.8) * 100);
            const confidenceSection = `
                <div class="confidence-meter">
                    <span>Confidence:</span>
                    <div class="confidence-bar">
                        <div class="confidence-fill" style="width: ${confidencePercent}%"></div>
                    </div>
                    <span>${confidencePercent}%</span>
                </div>
            `;

            messageDiv.innerHTML = `
                <div class="message-header">
                    <div class="avatar claude-avatar">
                        <i class="fas fa-robot"></i>
                    </div>
                    <div class="message-info">
                        <div class="sender-name">True Claude Agent</div>
                        <div class="message-time">${new Date().toLocaleTimeString()}</div>
                    </div>
                </div>
                <div class="message-content">
                    ${formatContent(data.response)}
                    ${reasoningSection}
                    ${toolSection}
                    ${confidenceSection}
                </div>
            `;

            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        // Format content
        function formatContent(content) {
            // Convert markdown-style formatting
            content = content.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
            content = content.replace(/\*(.*?)\*/g, '<em>$1</em>');
            content = content.replace(/`([^`]+)`/g, '<code>$1</code>');
            
            // Convert code blocks
            content = content.replace(/```(\w+)?\n([\s\S]*?)\n```/g, '<pre><code>$2</code></pre>');
            
            // Convert line breaks
            content = content.replace(/\n/g, '<br>');
            
            return content;
        }

        // Show thinking indicator
        function showThinking() {
            const messagesContainer = document.getElementById('chatMessages');
            const thinkingDiv = document.createElement('div');
            thinkingDiv.id = 'thinkingIndicator';
            thinkingDiv.className = 'thinking-indicator';
            thinkingDiv.innerHTML = `
                <i class="fas fa-robot"></i>
                <span>Claude is thinking deeply about your request</span>
                <div class="thinking-dots">
                    <div class="thinking-dot"></div>
                    <div class="thinking-dot"></div>
                    <div class="thinking-dot"></div>
                </div>
            `;
            
            messagesContainer.appendChild(thinkingDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        // Hide thinking indicator
        function hideThinking() {
            const thinkingIndicator = document.getElementById('thinkingIndicator');
            if (thinkingIndicator) {
                thinkingIndicator.remove();
            }
        }

        // Generate demo response
        function generateDemoResponse(message) {
            const responses = {
                "hello": {
                    response: "Hello! I'm the True Claude Agent, designed to work exactly like Claude. I can help you with programming, explanations, problem-solving, and much more. What would you like to work on together?",
                    confidence: 0.95,
                    reasoning_mode: "educational",
                    reasoning_chain: [
                        { description: "Recognized greeting and introduction request", confidence: 0.9 },
                        { description: "Determined appropriate welcoming response", confidence: 0.95 }
                    ],
                    tool_usage: []
                },
                "code": {
                    response: `I'll help you with coding! Here's my approach:

**Analysis**: I understand you need programming assistance. Let me provide a comprehensive solution.

**Implementation**:
\`\`\`python
def example_function(data):
    """
    Example function demonstrating clean code practices.
    
    Args:
        data: Input data to process
        
    Returns:
        Processed result
    """
    # Validate input
    if not data:
        raise ValueError("Data cannot be empty")
    
    # Process the data
    result = process_data(data)
    
    # Return result
    return result

def process_data(data):
    """Process the input data."""
    return f"Processed: {data}"

# Example usage
if __name__ == "__main__":
    result = example_function("test data")
    print(result)
\`\`\`

**Key Features**:
• Proper error handling and validation
• Clear documentation with docstrings
• Modular design for maintainability
• Example usage demonstration

**Next Steps**:
• Customize the function for your specific needs
• Add additional error handling as required
• Consider adding unit tests
• Integrate with your existing codebase

Let me know what specific programming task you'd like help with!`,
                    confidence: 0.88,
                    reasoning_mode: "creative",
                    reasoning_chain: [
                        { description: "Identified request for coding assistance", confidence: 0.9 },
                        { description: "Planned comprehensive code example with best practices", confidence: 0.85 },
                        { description: "Structured response with explanation and next steps", confidence: 0.9 }
                    ],
                    tool_usage: [
                        { tool: "codebase-retrieval", reasoning: "Gathering context about coding best practices" },
                        { tool: "save-file", reasoning: "Preparing to create example code file" }
                    ]
                }
            };

            // Simple keyword matching
            const lowerMessage = message.toLowerCase();
            
            if (lowerMessage.includes("hello") || lowerMessage.includes("hi")) {
                return responses.hello;
            } else if (lowerMessage.includes("code") || lowerMessage.includes("program") || lowerMessage.includes("function")) {
                return responses.code;
            } else {
                // Default comprehensive response
                return {
                    response: `I'll help you with that request. Here's my approach:

**Understanding**: I've analyzed your request and identified the key components that need to be addressed.

**My Reasoning**: 
• Breaking down the problem into manageable parts
• Considering the best approach based on the context
• Planning a comprehensive solution

**Solution**:
Based on your request, I recommend a systematic approach that addresses your specific needs while following best practices. The solution should be:

• **Clear and actionable** - Easy to understand and implement
• **Comprehensive** - Covering all important aspects
• **Maintainable** - Built for long-term success
• **Well-documented** - With clear explanations

**Implementation Strategy**:
1. Start with the core requirements
2. Build incrementally with testing
3. Add documentation and examples
4. Optimize and refine as needed

**Next Steps**:
• Review the approach and provide feedback
• Ask questions about any unclear aspects
• Let me know if you need more specific guidance
• Consider additional features or improvements

I'm here to help with any follow-up questions or clarifications you might need!`,
                    confidence: 0.75,
                    reasoning_mode: "practical",
                    reasoning_chain: [
                        { description: "Analyzed general request for assistance", confidence: 0.7 },
                        { description: "Planned comprehensive response approach", confidence: 0.8 },
                        { description: "Structured helpful guidance with next steps", confidence: 0.75 }
                    ],
                    tool_usage: [
                        { tool: "codebase-retrieval", reasoning: "Understanding context and requirements" },
                        { tool: "add_tasks", reasoning: "Planning structured approach for complex request" }
                    ]
                };
            }
        }

        // Event listeners
        document.getElementById('sendButton').addEventListener('click', sendMessage);
        
        document.getElementById('chatInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });

        // Auto-resize textarea
        document.getElementById('chatInput').addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = Math.min(this.scrollHeight, 120) + 'px';
        });
    </script>
</body>
</html>
