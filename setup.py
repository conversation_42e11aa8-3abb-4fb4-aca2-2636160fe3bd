#!/usr/bin/env python3
"""
Simple setup script for the augmented agent project.
Run this to set up the Python virtual environment and install dependencies.
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(cmd, cwd=None):
    """Run a command and print output."""
    print(f"Running: {' '.join(cmd)}")
    result = subprocess.run(cmd, cwd=cwd, capture_output=True, text=True)
    if result.stdout:
        print(result.stdout)
    if result.stderr:
        print(result.stderr)
    return result.returncode == 0

def main():
    project_root = Path(__file__).parent
    backend_dir = project_root / "backend"
    
    print("Setting up Augmented Agent project...")
    
    # Create virtual environment
    venv_path = backend_dir / "venv"
    if not venv_path.exists():
        print("\n1. Creating virtual environment...")
        if not run_command([sys.executable, "-m", "venv", "venv"], cwd=backend_dir):
            print("Failed to create virtual environment")
            return False
    else:
        print("\n1. Virtual environment already exists")
    
    # Determine the correct python executable in venv
    if os.name == 'nt':  # Windows
        python_exe = venv_path / "Scripts" / "python.exe"
        pip_exe = venv_path / "Scripts" / "pip.exe"
    else:  # Unix/Linux/macOS
        python_exe = venv_path / "bin" / "python"
        pip_exe = venv_path / "bin" / "pip"
    
    # Install dependencies
    print("\n2. Installing dependencies...")
    if not run_command([str(pip_exe), "install", "-r", "requirements.txt"], cwd=backend_dir):
        print("Failed to install dependencies")
        return False
    
    # Check for .env file
    env_file = project_root / ".env"
    if not env_file.exists():
        print("\n3. Creating .env file...")
        print("Please copy .env.example to .env and add your OpenAI API key")
        return True
    
    print("\n✅ Setup complete!")
    print("\nNext steps:")
    print("1. Make sure you have your OpenAI API key in the .env file")
    print("2. Run the backend:")
    if os.name == 'nt':
        print(f"   cd backend && {python_exe} -m uvicorn main:app --reload --port 8000")
    else:
        print(f"   cd backend && source venv/bin/activate && python -m uvicorn main:app --reload --port 8000")
    print("3. (Optional) Set up the React frontend in the frontend/ directory")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
