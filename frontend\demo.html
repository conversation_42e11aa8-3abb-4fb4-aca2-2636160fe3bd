<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Augmented Agent Demo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 10px;
        }
        .subtitle {
            text-align: center;
            color: #666;
            margin-bottom: 30px;
            font-style: italic;
        }
        .chat-log {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            min-height: 400px;
            max-height: 500px;
            overflow-y: auto;
            margin-bottom: 20px;
            font-family: 'Courier New', monospace;
        }
        .message {
            margin-bottom: 20px;
            padding: 15px;
            border-radius: 8px;
            animation: fadeIn 0.3s ease-in;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .user-message {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
        }
        .assistant-message {
            background: #f3e5f5;
            border-left: 4px solid #9c27b0;
        }
        .input-area {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
        }
        textarea {
            flex: 1;
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 8px;
            resize: vertical;
            font-family: Arial, sans-serif;
            font-size: 14px;
            transition: border-color 0.3s;
        }
        textarea:focus {
            outline: none;
            border-color: #2196f3;
        }
        button {
            padding: 15px 25px;
            background: linear-gradient(45deg, #2196f3, #21cbf3);
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(33, 150, 243, 0.3);
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        .examples {
            margin-top: 20px;
            padding: 20px;
            background: linear-gradient(45deg, #fff3cd, #ffeaa7);
            border-radius: 8px;
            border-left: 4px solid #ffc107;
        }
        .examples h3 {
            margin-top: 0;
            color: #333;
        }
        .example-button {
            background: linear-gradient(45deg, #ffc107, #ffb300);
            color: #333;
            margin: 5px;
            padding: 8px 15px;
            font-size: 14px;
            font-weight: normal;
        }
        .example-button:hover {
            background: linear-gradient(45deg, #ffb300, #ff8f00);
        }
        .demo-note {
            background: #e8f5e8;
            border: 2px solid #4caf50;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            text-align: center;
        }
        .demo-note strong {
            color: #2e7d32;
        }
        pre {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 Augmented Agent Demo</h1>
        <p class="subtitle">Interactive Programming Assistant</p>
        
        <div class="demo-note">
            <strong>🎯 Demo Mode:</strong> This is a standalone demo that simulates the augmented agent functionality without requiring a backend server or API keys.
        </div>
        
        <div id="chatLog" class="chat-log">
            <div class="message assistant-message">
                <strong>🤖 Assistant:</strong> Welcome to the Augmented Agent Demo! 
                <br><br>I'm a programming assistant that can help you with:
                <br>• 🐍 Running Python code and calculations
                <br>• 📊 Mathematical computations
                <br>• 🔧 Algorithm implementations
                <br>• 💡 Programming examples and solutions
                <br><br>This demo simulates the full agent experience. Try asking me something or use the examples below!
            </div>
        </div>
        
        <div class="input-area">
            <textarea id="userInput" rows="3" placeholder="Ask me anything! For example: 'Calculate the factorial of 5' or 'Show me the Fibonacci sequence'"></textarea>
            <button id="sendButton" onclick="sendMessage()">Send</button>
        </div>
        
        <div class="examples">
            <h3>💡 Try these examples:</h3>
            <button class="example-button" onclick="setInput('Calculate the factorial of 5')">Calculate factorial</button>
            <button class="example-button" onclick="setInput('Show me the first 10 Fibonacci numbers')">Fibonacci sequence</button>
            <button class="example-button" onclick="setInput('Create a function to reverse a string')">Reverse string</button>
            <button class="example-button" onclick="setInput('Do some mathematical calculations')">Math calculations</button>
            <button class="example-button" onclick="setInput('Hello, show me what you can do')">Say hello</button>
        </div>
    </div>

    <script>
        let chatHistory = [];
        
        function setInput(text) {
            document.getElementById('userInput').value = text;
        }
        
        function addMessage(role, content) {
            const chatLog = document.getElementById('chatLog');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${role}-message`;
            
            // Format content with proper line breaks and code blocks
            let formattedContent = content
                .replace(/\n/g, '<br>')
                .replace(/```python\n([\s\S]*?)\n```/g, '<pre>$1</pre>')
                .replace(/```([\s\S]*?)```/g, '<pre>$1</pre>');
            
            const icon = role === 'user' ? '👤' : '🤖';
            messageDiv.innerHTML = `<strong>${icon} ${role.charAt(0).toUpperCase() + role.slice(1)}:</strong> ${formattedContent}`;
            chatLog.appendChild(messageDiv);
            chatLog.scrollTop = chatLog.scrollHeight;
        }
        
        function simulatePythonExecution(code) {
            // Simulate Python code execution for demo purposes
            try {
                // Simple pattern matching for demo
                if (code.includes('factorial')) {
                    return 'Factorial of 5 is: 120';
                } else if (code.includes('fibonacci')) {
                    return `First 10 Fibonacci numbers:
F(0) = 0
F(1) = 1
F(2) = 1
F(3) = 2
F(4) = 3
F(5) = 5
F(6) = 8
F(7) = 13
F(8) = 21
F(9) = 34`;
                } else if (code.includes('reverse')) {
                    return `Original: Hello World
Reversed: dlroW olleH
Reversed (using loop): dlroW olleH`;
                } else if (code.includes('math')) {
                    return `Basic calculations:
2 + 2 = 4
10 * 5 = 50
Square root of 16 = 4.0
2 to the power of 8 = 256
Pi = 3.141593`;
                } else {
                    return `Welcome to the Augmented Agent Demo!
I can run Python code for you.
Try asking me to:
- Calculate factorial
- Generate Fibonacci numbers
- Reverse a string
- Do mathematical calculations

Here's a simple example:
Numbers: [1, 2, 3, 4, 5]
Squared: [1, 4, 9, 16, 25]`;
                }
            } catch (e) {
                return `Error: ${e.message}`;
            }
        }
        
        function generateDemoResponse(userInput) {
            const input = userInput.toLowerCase();
            
            if (input.includes('factorial')) {
                const code = `def factorial(n):
    if n <= 1:
        return 1
    return n * factorial(n - 1)

result = factorial(5)
print(f"Factorial of 5 is: {result}")`;
                const output = simulatePythonExecution(code);
                return `I'll calculate the factorial for you using Python:

\`\`\`python
${code}
\`\`\`

**Output:**
${output}`;
            }
            
            if (input.includes('fibonacci')) {
                const code = `def fibonacci(n):
    fib_sequence = [0, 1]
    for i in range(2, n):
        fib_sequence.append(fib_sequence[i-1] + fib_sequence[i-2])
    return fib_sequence

fib_numbers = fibonacci(10)
print("First 10 Fibonacci numbers:")
for i, num in enumerate(fib_numbers):
    print(f"F({i}) = {num}")`;
                const output = simulatePythonExecution(code);
                return `Here are the first 10 Fibonacci numbers:

\`\`\`python
${code}
\`\`\`

**Output:**
${output}`;
            }
            
            if (input.includes('reverse') && input.includes('string')) {
                const code = `def reverse_string(s):
    return s[::-1]

# Test the function
test_string = "Hello World"
reversed_str = reverse_string(test_string)
print(f"Original: {test_string}")
print(f"Reversed: {reversed_str}")

# Another way using a loop
def reverse_string_loop(s):
    result = ""
    for char in s:
        result = char + result
    return result

reversed_str2 = reverse_string_loop(test_string)
print(f"Reversed (using loop): {reversed_str2}")`;
                const output = simulatePythonExecution(code);
                return `Here's a Python function to reverse a string:

\`\`\`python
${code}
\`\`\`

**Output:**
${output}`;
            }
            
            if (input.includes('math') || input.includes('calculate')) {
                const code = `import math

# Some calculations
print("Basic calculations:")
print(f"2 + 2 = {2 + 2}")
print(f"10 * 5 = {10 * 5}")
print(f"Square root of 16 = {math.sqrt(16)}")
print(f"2 to the power of 8 = {2**8}")
print(f"Pi = {math.pi:.6f}")`;
                const output = simulatePythonExecution(code);
                return `Here are some mathematical calculations:

\`\`\`python
${code}
\`\`\`

**Output:**
${output}`;
            }
            
            if (input.includes('hello') || input.includes('hi')) {
                const code = `print("Hello from Python!")
print("I'm running inside the augmented agent!")
print("Current time:", end=" ")
import datetime
print(datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"))`;
                const output = `Hello from Python!
I'm running inside the augmented agent!
Current time: ${new Date().toLocaleString()}`;
                return `Hello! Let me greet you with some Python code:

\`\`\`python
${code}
\`\`\`

**Output:**
${output}`;
            }
            
            // Default response
            const code = `print("Welcome to the Augmented Agent Demo!")
print("I can run Python code for you.")
print("Try asking me to:")
print("- Calculate factorial")
print("- Generate Fibonacci numbers") 
print("- Reverse a string")
print("- Do mathematical calculations")

# Here's a simple example:
numbers = [1, 2, 3, 4, 5]
squared = [x**2 for x in numbers]
print(f"Numbers: {numbers}")
print(f"Squared: {squared}")`;
            const output = simulatePythonExecution(code);
            return `I'm a demo version of the augmented agent. Here's what I can do:

\`\`\`python
${code}
\`\`\`

**Output:**
${output}`;
        }
        
        function sendMessage() {
            const input = document.getElementById('userInput');
            const sendButton = document.getElementById('sendButton');
            const message = input.value.trim();
            
            if (!message) return;
            
            // Disable input while processing
            sendButton.disabled = true;
            sendButton.textContent = 'Processing...';
            
            // Add user message to chat
            addMessage('user', message);
            chatHistory.push({role: 'user', content: message});
            
            // Simulate processing delay
            setTimeout(() => {
                const response = generateDemoResponse(message);
                addMessage('assistant', response);
                chatHistory.push({role: 'assistant', content: response});
                
                // Re-enable input
                input.value = '';
                sendButton.disabled = false;
                sendButton.textContent = 'Send';
                input.focus();
            }, 1000);
        }
        
        // Allow Enter to send message (Shift+Enter for new line)
        document.getElementById('userInput').addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });
        
        // Focus on input when page loads
        document.getElementById('userInput').focus();
    </script>
</body>
</html>
