# backend/web_dev_tools.py
"""
Comprehensive Web Development Tools
Specialized tools for frontend, backend, database, and full-stack development
"""

import os
import json
import asyncio
import subprocess
import re
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
from datetime import datetime
import requests
import sqlite3
import yaml

class FrontendTools:
    """Tools for frontend development (<PERSON>act, Vue, Angular, vanilla JS)."""
    
    @staticmethod
    async def create_react_component(name: str, type: str = "functional", props: List[str] = None) -> str:
        """Generate a React component with proper structure."""
        props = props or []
        
        if type == "functional":
            imports = "import React from 'react';"
            if props:
                imports += "\nimport PropTypes from 'prop-types';"
            
            component = f"""import React from 'react';
{f"import PropTypes from 'prop-types';" if props else ""}

const {name} = ({{ {', '.join(props)} }}) => {{
  return (
    <div className="{name.lower()}">
      <h2>{name} Component</h2>
      {chr(10).join(f"      <p>{{props.{prop}}}</p>" for prop in props)}
    </div>
  );
}};

{f'''
{name}.propTypes = {{
{chr(10).join(f"  {prop}: PropTypes.string," for prop in props)}
}};
''' if props else ""}

export default {name};"""
        
        else:  # class component
            component = f"""import React, {{ Component }} from 'react';
{f"import PropTypes from 'prop-types';" if props else ""}

class {name} extends Component {{
  constructor(props) {{
    super(props);
    this.state = {{}};
  }}

  render() {{
    const {{ {', '.join(props)} }} = this.props;
    
    return (
      <div className="{name.lower()}">
        <h2>{name} Component</h2>
        {chr(10).join(f"        <p>{{this.props.{prop}}}</p>" for prop in props)}
      </div>
    );
  }}
}}

{f'''
{name}.propTypes = {{
{chr(10).join(f"  {prop}: PropTypes.string," for prop in props)}
}};
''' if props else ""}

export default {name};"""
        
        return component
    
    @staticmethod
    async def create_vue_component(name: str, props: List[str] = None, composition_api: bool = True) -> str:
        """Generate a Vue component with proper structure."""
        props = props or []
        
        if composition_api:
            component = f"""<template>
  <div class="{name.lower()}">
    <h2>{name} Component</h2>
    {chr(10).join(f'    <p>{{ {prop} }}</p>' for prop in props)}
  </div>
</template>

<script setup>
import {{ defineProps }} from 'vue';

const props = defineProps({{
{chr(10).join(f"  {prop}: String," for prop in props)}
}});
</script>

<style scoped>
.{name.lower()} {{
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  margin: 10px 0;
}}
</style>"""
        else:
            component = f"""<template>
  <div class="{name.lower()}">
    <h2>{name} Component</h2>
    {chr(10).join(f'    <p>{{ {prop} }}</p>' for prop in props)}
  </div>
</template>

<script>
export default {{
  name: '{name}',
  props: {{
{chr(10).join(f"    {prop}: String," for prop in props)}
  }},
  data() {{
    return {{}};
  }},
  methods: {{
    // Add your methods here
  }}
}};
</script>

<style scoped>
.{name.lower()} {{
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  margin: 10px 0;
}}
</style>"""
        
        return component
    
    @staticmethod
    async def create_angular_component(name: str, selector: str = None) -> Dict[str, str]:
        """Generate Angular component files."""
        selector = selector or f"app-{name.lower()}"
        
        # TypeScript component
        ts_content = f"""import {{ Component }} from '@angular/core';

@Component({{
  selector: '{selector}',
  templateUrl: './{name.lower()}.component.html',
  styleUrls: ['./{name.lower()}.component.css']
}})
export class {name}Component {{
  title = '{name} Component';
  
  constructor() {{ }}
  
  ngOnInit(): void {{
    // Initialization logic here
  }}
}}"""
        
        # HTML template
        html_content = f"""<div class="{name.lower()}">
  <h2>{{{{ title }}}}</h2>
  <p>{name} component is working!</p>
</div>"""
        
        # CSS styles
        css_content = f""".{name.lower()} {{
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  margin: 10px 0;
}}

h2 {{
  color: #333;
  margin-bottom: 15px;
}}"""
        
        # Spec file for testing
        spec_content = f"""import {{ ComponentFixture, TestBed }} from '@angular/core/testing';
import {{ {name}Component }} from './{name.lower()}.component';

describe('{name}Component', () => {{
  let component: {name}Component;
  let fixture: ComponentFixture<{name}Component>;

  beforeEach(async () => {{
    await TestBed.configureTestingModule({{
      declarations: [ {name}Component ]
    }})
    .compileComponents();
  }});

  beforeEach(() => {{
    fixture = TestBed.createComponent({name}Component);
    component = fixture.componentInstance;
    fixture.detectChanges();
  }});

  it('should create', () => {{
    expect(component).toBeTruthy();
  }});
}});"""
        
        return {
            f"{name.lower()}.component.ts": ts_content,
            f"{name.lower()}.component.html": html_content,
            f"{name.lower()}.component.css": css_content,
            f"{name.lower()}.component.spec.ts": spec_content
        }
    
    @staticmethod
    async def generate_css_framework(framework: str = "custom") -> str:
        """Generate CSS framework or utility classes."""
        if framework == "custom":
            return """/* Custom CSS Framework */

/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  color: #333;
}

/* Layout Utilities */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.justify-center {
  justify-content: center;
}

.items-center {
  align-items: center;
}

.grid {
  display: grid;
}

.grid-cols-2 {
  grid-template-columns: repeat(2, 1fr);
}

.grid-cols-3 {
  grid-template-columns: repeat(3, 1fr);
}

/* Spacing */
.p-4 { padding: 1rem; }
.p-8 { padding: 2rem; }
.m-4 { margin: 1rem; }
.m-8 { margin: 2rem; }

/* Typography */
.text-sm { font-size: 0.875rem; }
.text-base { font-size: 1rem; }
.text-lg { font-size: 1.125rem; }
.text-xl { font-size: 1.25rem; }
.text-2xl { font-size: 1.5rem; }

/* Colors */
.text-primary { color: #007bff; }
.text-secondary { color: #6c757d; }
.text-success { color: #28a745; }
.text-danger { color: #dc3545; }

.bg-primary { background-color: #007bff; }
.bg-secondary { background-color: #6c757d; }
.bg-light { background-color: #f8f9fa; }

/* Components */
.btn {
  display: inline-block;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 0.25rem;
  cursor: pointer;
  text-decoration: none;
  transition: all 0.2s;
}

.btn-primary {
  background-color: #007bff;
  color: white;
}

.btn-primary:hover {
  background-color: #0056b3;
}

.card {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  padding: 1.5rem;
  margin-bottom: 1rem;
}

/* Responsive */
@media (max-width: 768px) {
  .container {
    padding: 0 15px;
  }
  
  .grid-cols-2,
  .grid-cols-3 {
    grid-template-columns: 1fr;
  }
}"""
        
        elif framework == "tailwind":
            return """/* Tailwind CSS Configuration */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom Components */
@layer components {
  .btn {
    @apply px-4 py-2 rounded font-medium transition-colors;
  }
  
  .btn-primary {
    @apply bg-blue-500 text-white hover:bg-blue-600;
  }
  
  .btn-secondary {
    @apply bg-gray-500 text-white hover:bg-gray-600;
  }
  
  .card {
    @apply bg-white rounded-lg shadow-md p-6;
  }
  
  .form-input {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500;
  }
}"""

class BackendTools:
    """Tools for backend development (Node.js, Python, PHP)."""
    
    @staticmethod
    async def create_express_route(path: str, methods: List[str], middleware: List[str] = None) -> str:
        """Generate Express.js route with middleware."""
        middleware = middleware or []
        
        route_code = f"""const express = require('express');
const router = express.Router();
{chr(10).join(f"const {mw} = require('../middleware/{mw}');" for mw in middleware)}

"""
        
        for method in methods:
            method_lower = method.lower()
            middleware_str = ', '.join(middleware) if middleware else ''
            middleware_params = f", {middleware_str}" if middleware_str else ""
            
            route_code += f"""// {method.upper()} {path}
router.{method_lower}('{path}'{middleware_params}, async (req, res) => {{
  try {{
    // TODO: Implement {method.upper()} logic for {path}
    res.json({{
      message: '{method.upper()} {path} endpoint',
      data: null,
      timestamp: new Date().toISOString()
    }});
  }} catch (error) {{
    console.error('Error in {method.upper()} {path}:', error);
    res.status(500).json({{
      error: 'Internal server error',
      message: error.message
    }});
  }}
}});

"""
        
        route_code += "module.exports = router;"
        return route_code
    
    @staticmethod
    async def create_fastapi_route(path: str, methods: List[str], model_name: str = None) -> str:
        """Generate FastAPI route with Pydantic models."""
        
        route_code = f"""from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime

router = APIRouter()

"""
        
        if model_name:
            route_code += f"""class {model_name}(BaseModel):
    id: Optional[int] = None
    name: str
    description: Optional[str] = None
    created_at: Optional[datetime] = None

class {model_name}Create(BaseModel):
    name: str
    description: Optional[str] = None

class {model_name}Update(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None

"""
        
        for method in methods:
            method_lower = method.lower()
            
            if method_lower == 'get':
                route_code += f"""@router.get("{path}")
async def get_{path.replace('/', '_').strip('_')}():
    \"\"\"Get {path} endpoint.\"\"\"
    try:
        # TODO: Implement GET logic
        return {{
            "message": "GET {path} endpoint",
            "data": [],
            "timestamp": datetime.now().isoformat()
        }}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

"""
            elif method_lower == 'post' and model_name:
                route_code += f"""@router.post("{path}", response_model={model_name})
async def create_{path.replace('/', '_').strip('_')}(item: {model_name}Create):
    \"\"\"Create new {model_name.lower()}.\"\"\"
    try:
        # TODO: Implement POST logic
        new_item = {model_name}(
            id=1,
            name=item.name,
            description=item.description,
            created_at=datetime.now()
        )
        return new_item
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

"""
        
        return route_code
    
    @staticmethod
    async def create_database_model(name: str, fields: Dict[str, str], orm: str = "sqlalchemy") -> str:
        """Generate database model for different ORMs."""
        
        if orm == "sqlalchemy":
            model_code = f"""from sqlalchemy import Column, Integer, String, DateTime, Text, Boolean
from sqlalchemy.ext.declarative import declarative_base
from datetime import datetime

Base = declarative_base()

class {name}(Base):
    __tablename__ = '{name.lower()}s'
    
    id = Column(Integer, primary_key=True, index=True)
"""
            
            for field_name, field_type in fields.items():
                if field_type == "string":
                    model_code += f"    {field_name} = Column(String(255), nullable=False)\n"
                elif field_type == "text":
                    model_code += f"    {field_name} = Column(Text)\n"
                elif field_type == "integer":
                    model_code += f"    {field_name} = Column(Integer)\n"
                elif field_type == "boolean":
                    model_code += f"    {field_name} = Column(Boolean, default=False)\n"
                elif field_type == "datetime":
                    model_code += f"    {field_name} = Column(DateTime, default=datetime.utcnow)\n"
            
            model_code += f"""    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __repr__(self):
        return f"<{name}(id={{self.id}}, {list(fields.keys())[0] if fields else 'name'}={{self.{list(fields.keys())[0] if fields else 'name'}}})>"
"""
        
        elif orm == "mongoose":
            model_code = f"""const mongoose = require('mongoose');

const {name.lower()}Schema = new mongoose.Schema({{
"""
            
            for field_name, field_type in fields.items():
                if field_type == "string":
                    model_code += f"  {field_name}: {{ type: String, required: true }},\n"
                elif field_type == "text":
                    model_code += f"  {field_name}: {{ type: String }},\n"
                elif field_type == "integer":
                    model_code += f"  {field_name}: {{ type: Number }},\n"
                elif field_type == "boolean":
                    model_code += f"  {field_name}: {{ type: Boolean, default: false }},\n"
                elif field_type == "datetime":
                    model_code += f"  {field_name}: {{ type: Date, default: Date.now }},\n"
            
            model_code += f"""}}, {{
  timestamps: true
}});

module.exports = mongoose.model('{name}', {name.lower()}Schema);
"""
        
        return model_code

class DatabaseTools:
    """Tools for database operations and management."""
    
    @staticmethod
    async def generate_migration(name: str, operations: List[Dict[str, Any]], db_type: str = "postgresql") -> str:
        """Generate database migration script."""
        
        if db_type == "postgresql":
            migration = f"""-- Migration: {name}
-- Created: {datetime.now().isoformat()}

BEGIN;

"""
            
            for op in operations:
                if op["type"] == "create_table":
                    migration += f"""CREATE TABLE {op["table"]} (
    id SERIAL PRIMARY KEY,
"""
                    for field in op["fields"]:
                        migration += f"    {field['name']} {field['type']}"
                        if field.get("nullable", True) == False:
                            migration += " NOT NULL"
                        if field.get("default"):
                            migration += f" DEFAULT {field['default']}"
                        migration += ",\n"
                    
                    migration += "    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n"
                    migration += "    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n"
                    migration += ");\n\n"
                
                elif op["type"] == "add_column":
                    migration += f"ALTER TABLE {op['table']} ADD COLUMN {op['column']} {op['type']};\n\n"
                
                elif op["type"] == "drop_column":
                    migration += f"ALTER TABLE {op['table']} DROP COLUMN {op['column']};\n\n"
            
            migration += "COMMIT;"
        
        elif db_type == "mysql":
            migration = f"""-- Migration: {name}
-- Created: {datetime.now().isoformat()}

"""
            for op in operations:
                if op["type"] == "create_table":
                    migration += f"""CREATE TABLE {op["table"]} (
    id INT AUTO_INCREMENT PRIMARY KEY,
"""
                    for field in op["fields"]:
                        migration += f"    {field['name']} {field['type']}"
                        if field.get("nullable", True) == False:
                            migration += " NOT NULL"
                        migration += ",\n"
                    
                    migration += "    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n"
                    migration += "    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP\n"
                    migration += ");\n\n"
        
        return migration
    
    @staticmethod
    async def generate_seed_data(table: str, records: List[Dict[str, Any]], db_type: str = "postgresql") -> str:
        """Generate seed data script."""
        
        if not records:
            return f"-- No seed data for {table}"
        
        if db_type == "postgresql":
            columns = list(records[0].keys())
            seed_script = f"""-- Seed data for {table}
INSERT INTO {table} ({', '.join(columns)}) VALUES
"""
            
            values = []
            for record in records:
                record_values = []
                for col in columns:
                    value = record[col]
                    if isinstance(value, str):
                        record_values.append(f"'{value}'")
                    elif value is None:
                        record_values.append("NULL")
                    else:
                        record_values.append(str(value))
                values.append(f"({', '.join(record_values)})")
            
            seed_script += ',\n'.join(values) + ";"
        
        return seed_script
