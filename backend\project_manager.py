# backend/project_manager.py
"""
Comprehensive Project Management System
Handles project scaffolding, dependency management, build systems, and workflow automation
"""

import os
import json
import yaml
import asyncio
import subprocess
import shutil
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
from datetime import datetime
import zipfile
import requests

class ProjectScaffolder:
    """Handles project scaffolding and template generation."""
    
    def __init__(self, workspace_path: str = None):
        self.workspace_path = Path(workspace_path or os.getcwd())
        self.templates_cache = {}
    
    async def scaffold_project(self, project_config: Dict[str, Any]) -> Dict[str, Any]:
        """Create complete project structure with all necessary files."""
        try:
            project_name = project_config["name"]
            project_type = project_config["type"]
            project_path = self.workspace_path / project_name
            
            # Create project directory
            project_path.mkdir(exist_ok=True)
            
            # Generate based on project type
            if project_type == "react":
                return await self._scaffold_react_project(project_path, project_config)
            elif project_type == "vue":
                return await self._scaffold_vue_project(project_path, project_config)
            elif project_type == "angular":
                return await self._scaffold_angular_project(project_path, project_config)
            elif project_type == "node_api":
                return await self._scaffold_node_api_project(project_path, project_config)
            elif project_type == "python_api":
                return await self._scaffold_python_api_project(project_path, project_config)
            elif project_type == "fullstack":
                return await self._scaffold_fullstack_project(project_path, project_config)
            elif project_type == "static":
                return await self._scaffold_static_project(project_path, project_config)
            else:
                return {"success": False, "error": f"Unknown project type: {project_type}"}
                
        except Exception as e:
            return {"success": False, "error": f"Scaffolding failed: {str(e)}"}
    
    async def _scaffold_react_project(self, project_path: Path, config: Dict[str, Any]) -> Dict[str, Any]:
        """Scaffold a complete React project."""
        
        # Create directory structure
        directories = [
            "src/components", "src/pages", "src/hooks", "src/utils", 
            "src/styles", "src/assets", "src/context", "src/services",
            "public", "tests", "docs"
        ]
        
        for dir_path in directories:
            (project_path / dir_path).mkdir(parents=True, exist_ok=True)
        
        # Generate package.json
        package_json = {
            "name": config["name"],
            "version": "0.1.0",
            "private": True,
            "dependencies": {
                "react": "^18.2.0",
                "react-dom": "^18.2.0",
                "react-router-dom": "^6.8.0",
                "axios": "^1.3.0",
                "@emotion/react": "^11.10.0",
                "@emotion/styled": "^11.10.0"
            },
            "devDependencies": {
                "@vitejs/plugin-react": "^3.1.0",
                "vite": "^4.1.0",
                "eslint": "^8.35.0",
                "eslint-plugin-react": "^7.32.0",
                "@testing-library/react": "^14.0.0",
                "@testing-library/jest-dom": "^5.16.0",
                "vitest": "^0.28.0"
            },
            "scripts": {
                "dev": "vite",
                "build": "vite build",
                "preview": "vite preview",
                "test": "vitest",
                "lint": "eslint src/",
                "format": "prettier --write src/"
            }
        }
        
        (project_path / "package.json").write_text(json.dumps(package_json, indent=2))
        
        # Generate Vite config
        vite_config = '''import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

export default defineConfig({
  plugins: [react()],
  server: {
    port: 3000,
    open: true
  },
  build: {
    outDir: 'dist',
    sourcemap: true
  }
})'''
        
        (project_path / "vite.config.js").write_text(vite_config)
        
        # Generate main App component
        app_component = f'''import React from 'react';
import {{ BrowserRouter as Router, Routes, Route }} from 'react-router-dom';
import './App.css';
import Header from './components/Header';
import Footer from './components/Footer';
import Home from './pages/Home';
import About from './pages/About';

function App() {{
  return (
    <Router>
      <div className="App">
        <Header />
        <main className="main-content">
          <Routes>
            <Route path="/" element={{<Home />}} />
            <Route path="/about" element={{<About />}} />
          </Routes>
        </main>
        <Footer />
      </div>
    </Router>
  );
}}

export default App;'''
        
        (project_path / "src" / "App.js").write_text(app_component)
        
        # Generate index.js
        index_js = '''import React from 'react';
import ReactDOM from 'react-dom/client';
import './index.css';
import App from './App';

const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);'''
        
        (project_path / "src" / "index.js").write_text(index_js)
        
        # Generate HTML template
        html_template = f'''<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>{config["name"]}</title>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/index.js"></script>
  </body>
</html>'''
        
        (project_path / "index.html").write_text(html_template)
        
        # Generate basic components
        await self._generate_react_components(project_path, config)
        
        # Generate CSS files
        await self._generate_react_styles(project_path)
        
        # Generate configuration files
        await self._generate_common_config_files(project_path, "react")
        
        return {
            "success": True,
            "message": f"React project '{config['name']}' scaffolded successfully",
            "path": str(project_path),
            "next_steps": [
                "cd " + config["name"],
                "npm install",
                "npm run dev"
            ]
        }
    
    async def _generate_react_components(self, project_path: Path, config: Dict[str, Any]) -> None:
        """Generate basic React components."""
        
        # Header component
        header_component = '''import React from 'react';
import { Link } from 'react-router-dom';
import './Header.css';

const Header = () => {
  return (
    <header className="header">
      <div className="container">
        <Link to="/" className="logo">
          <h1>''' + config["name"] + '''</h1>
        </Link>
        <nav className="nav">
          <Link to="/" className="nav-link">Home</Link>
          <Link to="/about" className="nav-link">About</Link>
        </nav>
      </div>
    </header>
  );
};

export default Header;'''
        
        (project_path / "src" / "components" / "Header.js").write_text(header_component)
        
        # Footer component
        footer_component = '''import React from 'react';
import './Footer.css';

const Footer = () => {
  return (
    <footer className="footer">
      <div className="container">
        <p>&copy; 2024 ''' + config["name"] + '''. All rights reserved.</p>
      </div>
    </footer>
  );
};

export default Footer;'''
        
        (project_path / "src" / "components" / "Footer.js").write_text(footer_component)
        
        # Home page
        home_page = '''import React from 'react';
import './Home.css';

const Home = () => {
  return (
    <div className="home">
      <div className="hero">
        <h1>Welcome to ''' + config["name"] + '''</h1>
        <p>''' + config.get("description", "A modern React application") + '''</p>
        <button className="cta-button">Get Started</button>
      </div>
    </div>
  );
};

export default Home;'''
        
        (project_path / "src" / "pages" / "Home.js").write_text(home_page)
        
        # About page
        about_page = '''import React from 'react';
import './About.css';

const About = () => {
  return (
    <div className="about">
      <div className="container">
        <h1>About ''' + config["name"] + '''</h1>
        <p>This is the about page for our application.</p>
      </div>
    </div>
  );
};

export default About;'''
        
        (project_path / "src" / "pages" / "About.js").write_text(about_page)
    
    async def _generate_react_styles(self, project_path: Path) -> None:
        """Generate CSS files for React components."""
        
        # Main App CSS
        app_css = '''/* App.css */
.App {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
}

/* Global Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  line-height: 1.6;
  color: #333;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}'''
        
        (project_path / "src" / "App.css").write_text(app_css)
        
        # Index CSS
        index_css = '''/* index.css */
body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}'''
        
        (project_path / "src" / "index.css").write_text(index_css)
        
        # Component-specific CSS files
        header_css = '''.header {
  background: #fff;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 20px;
}

.logo h1 {
  color: #007bff;
  text-decoration: none;
}

.nav {
  display: flex;
  gap: 2rem;
}

.nav-link {
  text-decoration: none;
  color: #333;
  font-weight: 500;
  transition: color 0.2s;
}

.nav-link:hover {
  color: #007bff;
}'''
        
        (project_path / "src" / "components" / "Header.css").write_text(header_css)
        
        footer_css = '''.footer {
  background: #f8f9fa;
  padding: 2rem 0;
  margin-top: auto;
  text-align: center;
  border-top: 1px solid #e9ecef;
}'''
        
        (project_path / "src" / "components" / "Footer.css").write_text(footer_css)
        
        home_css = '''.home {
  padding: 4rem 0;
}

.hero {
  text-align: center;
  max-width: 600px;
  margin: 0 auto;
}

.hero h1 {
  font-size: 3rem;
  margin-bottom: 1rem;
  color: #333;
}

.hero p {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  color: #666;
}

.cta-button {
  background: #007bff;
  color: white;
  border: none;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  border-radius: 5px;
  cursor: pointer;
  transition: background 0.2s;
}

.cta-button:hover {
  background: #0056b3;
}'''
        
        (project_path / "src" / "pages" / "Home.css").write_text(home_css)
        
        about_css = '''.about {
  padding: 4rem 0;
}

.about h1 {
  margin-bottom: 2rem;
  color: #333;
}

.about p {
  font-size: 1.1rem;
  line-height: 1.8;
  color: #666;
}'''
        
        (project_path / "src" / "pages" / "About.css").write_text(about_css)
    
    async def _generate_common_config_files(self, project_path: Path, project_type: str) -> None:
        """Generate common configuration files."""
        
        # .gitignore
        gitignore_content = '''# Dependencies
node_modules/
/.pnp
.pnp.js

# Testing
/coverage

# Production
/build
/dist

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache'''
        
        (project_path / ".gitignore").write_text(gitignore_content)
        
        # ESLint config
        eslint_config = '''{
  "env": {
    "browser": true,
    "es2021": true,
    "node": true
  },
  "extends": [
    "eslint:recommended",
    "plugin:react/recommended"
  ],
  "parserOptions": {
    "ecmaFeatures": {
      "jsx": true
    },
    "ecmaVersion": 12,
    "sourceType": "module"
  },
  "plugins": [
    "react"
  ],
  "rules": {
    "react/react-in-jsx-scope": "off",
    "react/prop-types": "warn"
  },
  "settings": {
    "react": {
      "version": "detect"
    }
  }
}'''
        
        (project_path / ".eslintrc.json").write_text(eslint_config)
        
        # Prettier config
        prettier_config = '''{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 80,
  "tabWidth": 2
}'''
        
        (project_path / ".prettierrc").write_text(prettier_config)
        
        # README.md
        readme_content = f'''# {project_path.name}

A modern {project_type.title()} application built with best practices.

## 🚀 Getting Started

### Prerequisites

- Node.js (v16 or higher)
- npm or yarn

### Installation

1. Clone the repository
```bash
git clone <repository-url>
cd {project_path.name}
```

2. Install dependencies
```bash
npm install
```

3. Start the development server
```bash
npm run dev
```

4. Open [http://localhost:3000](http://localhost:3000) to view it in the browser.

## 📁 Project Structure

```
{project_path.name}/
├── public/             # Static files
├── src/
│   ├── components/     # Reusable components
│   ├── pages/         # Page components
│   ├── hooks/         # Custom hooks
│   ├── utils/         # Utility functions
│   ├── styles/        # Global styles
│   └── assets/        # Images, fonts, etc.
├── tests/             # Test files
└── docs/              # Documentation
```

## 🛠️ Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm test` - Run tests
- `npm run lint` - Run ESLint
- `npm run format` - Format code with Prettier

## 🧪 Testing

```bash
npm test
```

## 🚀 Deployment

```bash
npm run build
```

## 📝 License

MIT

---

Generated by Full-Stack AI Agent 🤖
'''
        
        (project_path / "README.md").write_text(readme_content)
