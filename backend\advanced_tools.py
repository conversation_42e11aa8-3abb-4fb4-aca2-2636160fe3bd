# backend/advanced_tools.py
"""
Advanced tool system that mimics sophisticated AI assistant capabilities.
Includes codebase analysis, file operations, web search, process management, and testing.
"""

import subprocess
import tempfile
import os
import ast
import re
import json
import asyncio
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import shutil

class CodeAnalyzer:
    """Advanced code analysis capabilities."""
    
    @staticmethod
    def analyze_python_code(code: str) -> Dict[str, Any]:
        """Analyze Python code structure and complexity."""
        try:
            tree = ast.parse(code)
            analysis = {
                "functions": [],
                "classes": [],
                "imports": [],
                "complexity": 0,
                "lines_of_code": len(code.splitlines()),
                "syntax_valid": True
            }
            
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    analysis["functions"].append({
                        "name": node.name,
                        "line": node.lineno,
                        "args": [arg.arg for arg in node.args.args],
                        "docstring": ast.get_docstring(node)
                    })
                elif isinstance(node, ast.ClassDef):
                    analysis["classes"].append({
                        "name": node.name,
                        "line": node.lineno,
                        "methods": [n.name for n in node.body if isinstance(n, ast.FunctionDef)],
                        "docstring": ast.get_docstring(node)
                    })
                elif isinstance(node, (ast.Import, ast.ImportFrom)):
                    if isinstance(node, ast.Import):
                        analysis["imports"].extend([alias.name for alias in node.names])
                    else:
                        analysis["imports"].append(f"{node.module}.{alias.name}" for alias in node.names)
            
            return analysis
        except SyntaxError as e:
            return {
                "syntax_valid": False,
                "error": str(e),
                "line": e.lineno,
                "offset": e.offset
            }

async def run_python_advanced(code: str, timeout: int = 10, capture_output: bool = True) -> Dict[str, Any]:
    """Advanced Python execution with detailed analysis and output capture."""
    
    # Analyze code first
    analysis = CodeAnalyzer.analyze_python_code(code)
    
    if not analysis.get("syntax_valid", True):
        return {
            "success": False,
            "error": f"Syntax Error: {analysis['error']}",
            "analysis": analysis,
            "output": "",
            "execution_time": 0
        }
    
    start_time = datetime.now()
    
    with tempfile.TemporaryDirectory() as td:
        script_path = Path(td) / "script.py"
        script_path.write_text(code)
        
        try:
            python_cmd = "python" if os.name == 'nt' else "python3"
            
            if capture_output:
                proc = subprocess.run(
                    [python_cmd, str(script_path)],
                    capture_output=True,
                    text=True,
                    timeout=timeout,
                    check=False
                )
                
                execution_time = (datetime.now() - start_time).total_seconds()
                
                return {
                    "success": proc.returncode == 0,
                    "output": proc.stdout.strip(),
                    "error": proc.stderr.strip() if proc.stderr else None,
                    "return_code": proc.returncode,
                    "execution_time": execution_time,
                    "analysis": analysis
                }
            else:
                # For interactive execution
                proc = subprocess.Popen(
                    [python_cmd, str(script_path)],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True
                )
                
                stdout, stderr = proc.communicate(timeout=timeout)
                execution_time = (datetime.now() - start_time).total_seconds()
                
                return {
                    "success": proc.returncode == 0,
                    "output": stdout.strip(),
                    "error": stderr.strip() if stderr else None,
                    "return_code": proc.returncode,
                    "execution_time": execution_time,
                    "analysis": analysis
                }
                
        except subprocess.TimeoutExpired:
            return {
                "success": False,
                "error": f"Execution timed out after {timeout} seconds",
                "output": "",
                "execution_time": timeout,
                "analysis": analysis
            }
        except Exception as e:
            return {
                "success": False,
                "error": f"Execution error: {str(e)}",
                "output": "",
                "execution_time": (datetime.now() - start_time).total_seconds(),
                "analysis": analysis
            }

async def read_file_advanced(path: str, encoding: str = "utf-8", max_size: int = 1024*1024) -> Dict[str, Any]:
    """Advanced file reading with metadata and analysis."""
    
    # Security check - ensure path is within allowed directory
    ROOT = Path(__file__).resolve().parents[1]
    try:
        target = (ROOT / path).resolve()
        if not str(target).startswith(str(ROOT)):
            return {
                "success": False,
                "error": "Access denied: Path outside project directory",
                "content": None
            }
        
        if not target.exists():
            return {
                "success": False,
                "error": "File not found",
                "content": None
            }
        
        # Check file size
        file_size = target.stat().st_size
        if file_size > max_size:
            return {
                "success": False,
                "error": f"File too large ({file_size} bytes, max {max_size})",
                "content": None
            }
        
        # Read file content
        content = target.read_text(encoding=encoding)
        
        # Analyze file
        analysis = {
            "size": file_size,
            "lines": len(content.splitlines()),
            "extension": target.suffix,
            "modified": datetime.fromtimestamp(target.stat().st_mtime).isoformat(),
            "encoding": encoding
        }
        
        # Additional analysis for code files
        if target.suffix == ".py":
            code_analysis = CodeAnalyzer.analyze_python_code(content)
            analysis["code_analysis"] = code_analysis
        
        return {
            "success": True,
            "content": content,
            "path": str(target.relative_to(ROOT)),
            "analysis": analysis
        }
        
    except UnicodeDecodeError:
        return {
            "success": False,
            "error": f"Cannot decode file with {encoding} encoding",
            "content": None
        }
    except Exception as e:
        return {
            "success": False,
            "error": f"Error reading file: {str(e)}",
            "content": None
        }

async def write_file_advanced(path: str, content: str, encoding: str = "utf-8", backup: bool = True) -> Dict[str, Any]:
    """Advanced file writing with backup and validation."""
    
    ROOT = Path(__file__).resolve().parents[1]
    try:
        target = (ROOT / path).resolve()
        if not str(target).startswith(str(ROOT)):
            return {
                "success": False,
                "error": "Access denied: Path outside project directory"
            }
        
        # Create backup if file exists and backup is requested
        backup_path = None
        if backup and target.exists():
            backup_path = target.with_suffix(target.suffix + f".backup.{datetime.now().strftime('%Y%m%d_%H%M%S')}")
            shutil.copy2(target, backup_path)
        
        # Create directory if it doesn't exist
        target.parent.mkdir(parents=True, exist_ok=True)
        
        # Validate content for Python files
        validation_result = None
        if target.suffix == ".py":
            validation_result = CodeAnalyzer.analyze_python_code(content)
            if not validation_result.get("syntax_valid", True):
                return {
                    "success": False,
                    "error": f"Invalid Python syntax: {validation_result['error']}",
                    "validation": validation_result
                }
        
        # Write file
        target.write_text(content, encoding=encoding)
        
        return {
            "success": True,
            "path": str(target.relative_to(ROOT)),
            "size": len(content.encode(encoding)),
            "lines": len(content.splitlines()),
            "backup_path": str(backup_path.relative_to(ROOT)) if backup_path else None,
            "validation": validation_result
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": f"Error writing file: {str(e)}"
        }

async def analyze_codebase(directory: str = ".", include_patterns: List[str] = None, exclude_patterns: List[str] = None) -> Dict[str, Any]:
    """Analyze entire codebase structure and provide insights."""
    
    if include_patterns is None:
        include_patterns = ["*.py", "*.js", "*.ts", "*.html", "*.css", "*.md"]
    
    if exclude_patterns is None:
        exclude_patterns = ["__pycache__", "node_modules", ".git", "venv", "*.pyc"]
    
    ROOT = Path(__file__).resolve().parents[1]
    target_dir = ROOT / directory
    
    if not target_dir.exists():
        return {
            "success": False,
            "error": "Directory not found"
        }
    
    analysis = {
        "total_files": 0,
        "total_lines": 0,
        "file_types": {},
        "largest_files": [],
        "python_analysis": {
            "total_functions": 0,
            "total_classes": 0,
            "complexity_score": 0
        },
        "structure": {},
        "issues": []
    }
    
    try:
        for file_path in target_dir.rglob("*"):
            if file_path.is_file():
                # Check if file should be included
                if not any(file_path.match(pattern) for pattern in include_patterns):
                    continue
                
                # Check if file should be excluded
                if any(part in exclude_patterns for part in file_path.parts):
                    continue
                
                try:
                    content = file_path.read_text(encoding="utf-8")
                    lines = len(content.splitlines())
                    
                    analysis["total_files"] += 1
                    analysis["total_lines"] += lines
                    
                    # Track file types
                    ext = file_path.suffix or "no_extension"
                    if ext not in analysis["file_types"]:
                        analysis["file_types"][ext] = {"count": 0, "lines": 0}
                    analysis["file_types"][ext]["count"] += 1
                    analysis["file_types"][ext]["lines"] += lines
                    
                    # Track largest files
                    analysis["largest_files"].append({
                        "path": str(file_path.relative_to(ROOT)),
                        "lines": lines,
                        "size": file_path.stat().st_size
                    })
                    
                    # Python-specific analysis
                    if file_path.suffix == ".py":
                        code_analysis = CodeAnalyzer.analyze_python_code(content)
                        if code_analysis.get("syntax_valid", True):
                            analysis["python_analysis"]["total_functions"] += len(code_analysis.get("functions", []))
                            analysis["python_analysis"]["total_classes"] += len(code_analysis.get("classes", []))
                        else:
                            analysis["issues"].append({
                                "file": str(file_path.relative_to(ROOT)),
                                "type": "syntax_error",
                                "message": code_analysis.get("error", "Unknown syntax error")
                            })
                
                except UnicodeDecodeError:
                    # Skip binary files
                    continue
                except Exception as e:
                    analysis["issues"].append({
                        "file": str(file_path.relative_to(ROOT)),
                        "type": "read_error",
                        "message": str(e)
                    })
        
        # Sort largest files
        analysis["largest_files"].sort(key=lambda x: x["lines"], reverse=True)
        analysis["largest_files"] = analysis["largest_files"][:10]  # Top 10
        
        return {
            "success": True,
            "analysis": analysis
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": f"Error analyzing codebase: {str(e)}"
        }

async def web_search_simulation(query: str, num_results: int = 5) -> Dict[str, Any]:
    """Simulate web search functionality (placeholder for real implementation)."""
    
    # This is a simulation - in a real implementation, you'd use a search API
    simulated_results = [
        {
            "title": f"Documentation for {query}",
            "url": f"https://docs.example.com/{query.replace(' ', '-')}",
            "snippet": f"Official documentation and examples for {query}. Learn how to use {query} effectively in your projects."
        },
        {
            "title": f"{query} Tutorial - Step by Step Guide",
            "url": f"https://tutorial.example.com/{query.replace(' ', '-')}",
            "snippet": f"Complete tutorial covering {query} from basics to advanced concepts. Includes code examples and best practices."
        },
        {
            "title": f"Stack Overflow - {query} Questions",
            "url": f"https://stackoverflow.com/questions/tagged/{query.replace(' ', '+')}",
            "snippet": f"Community questions and answers about {query}. Find solutions to common problems and implementation tips."
        }
    ]
    
    return {
        "success": True,
        "query": query,
        "results": simulated_results[:num_results],
        "total_results": len(simulated_results)
    }

async def run_tests(test_path: str = "tests", pattern: str = "test_*.py") -> Dict[str, Any]:
    """Run tests and provide detailed results."""
    
    ROOT = Path(__file__).resolve().parents[1]
    test_dir = ROOT / test_path
    
    if not test_dir.exists():
        return {
            "success": False,
            "error": f"Test directory '{test_path}' not found"
        }
    
    try:
        # Run pytest if available, otherwise run unittest
        python_cmd = "python" if os.name == 'nt' else "python3"
        
        # Try pytest first
        try:
            proc = subprocess.run(
                [python_cmd, "-m", "pytest", str(test_dir), "-v", "--tb=short"],
                capture_output=True,
                text=True,
                timeout=60,
                cwd=ROOT
            )
            
            return {
                "success": proc.returncode == 0,
                "output": proc.stdout,
                "error": proc.stderr if proc.stderr else None,
                "test_runner": "pytest",
                "return_code": proc.returncode
            }
            
        except FileNotFoundError:
            # Fall back to unittest
            proc = subprocess.run(
                [python_cmd, "-m", "unittest", "discover", "-s", str(test_dir), "-p", pattern, "-v"],
                capture_output=True,
                text=True,
                timeout=60,
                cwd=ROOT
            )
            
            return {
                "success": proc.returncode == 0,
                "output": proc.stdout,
                "error": proc.stderr if proc.stderr else None,
                "test_runner": "unittest",
                "return_code": proc.returncode
            }
            
    except subprocess.TimeoutExpired:
        return {
            "success": False,
            "error": "Tests timed out after 60 seconds",
            "output": "",
            "test_runner": "unknown"
        }
    except Exception as e:
        return {
            "success": False,
            "error": f"Error running tests: {str(e)}",
            "output": "",
            "test_runner": "unknown"
        }

async def manage_processes(action: str, command: str = None, process_id: int = None) -> Dict[str, Any]:
    """Manage system processes (start, stop, list)."""
    
    if action == "list":
        try:
            if os.name == 'nt':
                proc = subprocess.run(
                    ["tasklist", "/fo", "csv"],
                    capture_output=True,
                    text=True,
                    timeout=10
                )
            else:
                proc = subprocess.run(
                    ["ps", "aux"],
                    capture_output=True,
                    text=True,
                    timeout=10
                )
            
            return {
                "success": proc.returncode == 0,
                "processes": proc.stdout,
                "action": "list"
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"Error listing processes: {str(e)}",
                "action": "list"
            }
    
    elif action == "start" and command:
        try:
            # Start process in background
            proc = subprocess.Popen(
                command.split(),
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            return {
                "success": True,
                "process_id": proc.pid,
                "command": command,
                "action": "start"
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"Error starting process: {str(e)}",
                "action": "start"
            }
    
    elif action == "stop" and process_id:
        try:
            if os.name == 'nt':
                subprocess.run(["taskkill", "/PID", str(process_id), "/F"], check=True)
            else:
                subprocess.run(["kill", str(process_id)], check=True)
            
            return {
                "success": True,
                "process_id": process_id,
                "action": "stop"
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"Error stopping process: {str(e)}",
                "action": "stop"
            }
    
    else:
        return {
            "success": False,
            "error": "Invalid action or missing parameters",
            "action": action
        }
